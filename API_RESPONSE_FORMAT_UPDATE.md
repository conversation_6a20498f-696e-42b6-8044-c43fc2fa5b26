# Background Check API Response Format Update

## Overview
Updated the `offenderSearchByLocation` function to handle the new API response format that returns a structured object instead of a direct array.

## Changes Made

### 1. Updated Response Parsing Logic

**Before (Expected Direct Array):**
```javascript
const offenders: any[] = await response.json();

if (!Array.isArray(offenders)) {
  console.warn('Unexpected API response format:', offenders);
  return [];
}
```

**After (Handles Both Formats):**
```javascript
const responseData = await response.json();

// Handle new API response format with nested structure
let allOffenders: any[] = [];

if (Array.isArray(responseData)) {
  // Backward compatibility: if response is still a direct array
  allOffenders = responseData;
} else if (responseData && typeof responseData === 'object') {
  // New format: extract offenders from nested structure
  const exactMatches = Array.isArray(responseData.exactMatches) ? responseData.exactMatches : [];
  const nearbyOffenders = Array.isArray(responseData.nearbyOffenders) ? responseData.nearbyOffenders : [];
  
  // Combine both arrays, with exact matches first
  allOffenders = [...exactMatches, ...nearbyOffenders];
}
```

### 2. New API Response Structure Support

The function now handles the new response format:
```json
{
  "exactMatches": [
    {
      "name": "John Doe",
      "address": "123 Main St",
      "location": "25.7657326,-80.2249206",
      // ... other offender properties
    }
  ],
  "nearbyOffenders": [
    {
      "name": "Jane Smith", 
      "address": "456 Oak St",
      "location": "25.7657326,-80.2249206",
      // ... other offender properties
    }
  ],
  "totalCount": 2,
  "searchQuery": {
    "address": "343 MAJORCA AVENUE CORAL GABLES, FL 33134",
    "latitude": 25.7553134,
    "longitude": -80.2619766,
    "memberId": "fe3aa001-8465-425b-b6ec-a2de78121db1"
  },
  "queueId": "unique-search-id"
}
```

### 3. Enhanced Logging and Debugging

**Added Response Logging:**
```javascript
console.log('Background check API response received:', {
  hasExactMatches: responseData?.exactMatches ? responseData.exactMatches.length : 'N/A',
  hasNearbyOffenders: responseData?.nearbyOffenders ? responseData.nearbyOffenders.length : 'N/A',
  totalCount: responseData?.totalCount || 'N/A',
  queueId: responseData?.queueId || 'N/A'
});
```

**Improved Error Messages:**
```javascript
console.warn('Unexpected API response format. Expected array or object with exactMatches/nearbyOffenders properties:', {
  responseType: typeof responseData,
  hasExactMatches: 'exactMatches' in (responseData || {}),
  hasNearbyOffenders: 'nearbyOffenders' in (responseData || {}),
  responseKeys: responseData ? Object.keys(responseData) : 'null/undefined'
});
```

### 4. Backward Compatibility

The implementation maintains backward compatibility:
- If the API returns a direct array (old format), it processes it as before
- If the API returns the new object format, it extracts and combines the offender arrays
- All existing data transformation and UI logic remains unchanged

## Benefits

### 1. **Structured Data Access**
- Clear separation between exact matches and nearby offenders
- Access to additional metadata (totalCount, queueId, searchQuery)
- Better debugging and monitoring capabilities

### 2. **Enhanced User Experience**
- Exact matches are processed first, giving priority to more relevant results
- Better logging helps with troubleshooting
- Maintains existing UI behavior and appearance

### 3. **Future-Proof Design**
- Handles both old and new response formats
- Easy to extend for additional response properties
- Robust error handling for unexpected formats

### 4. **Improved Debugging**
- Detailed logging of response structure
- Clear error messages for troubleshooting
- Visibility into API response metadata

## Testing Scenarios

### 1. **New Format with Both Arrays**
```json
{
  "exactMatches": [offender1, offender2],
  "nearbyOffenders": [offender3, offender4],
  "totalCount": 4
}
```
Result: All 4 offenders processed, exact matches first

### 2. **New Format with Only Exact Matches**
```json
{
  "exactMatches": [offender1],
  "nearbyOffenders": [],
  "totalCount": 1
}
```
Result: 1 offender processed

### 3. **New Format with Only Nearby Offenders**
```json
{
  "exactMatches": [],
  "nearbyOffenders": [offender1, offender2],
  "totalCount": 2
}
```
Result: 2 offenders processed

### 4. **Old Format (Backward Compatibility)**
```json
[offender1, offender2, offender3]
```
Result: All 3 offenders processed as before

### 5. **Empty Results**
```json
{
  "exactMatches": [],
  "nearbyOffenders": [],
  "totalCount": 0
}
```
Result: Empty array returned, "No offenders found" logged

## Deployment Notes

- **No Breaking Changes**: Existing functionality preserved
- **No Configuration Changes**: Works with current setup
- **Enhanced Monitoring**: New logs provide better visibility
- **Graceful Degradation**: Handles unexpected formats safely

The update ensures the background check functionality continues to work seamlessly while supporting the new, more structured API response format.
