/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import React from 'react';

interface NetworkToggleProps {
  activeView: 'networks' | 'connected';
  onToggle: (view: 'networks' | 'connected') => void;
}

function NetworkToggle({ activeView, onToggle }: NetworkToggleProps) {
  const tabs = [
    { key: 'networks', title: 'Networks' },
    { key: 'connected', title: 'Connected Networks' },
  ];

  return (
    <div className="flex rounded-lg overflow-hidden border border-gray-300">
      {tabs.map((tab, index) => (
        <div
          key={tab.key}
          onClick={() => onToggle(tab.key as 'networks' | 'connected')}
          style={{
            background: activeView !== tab.key
              ? 'linear-gradient(180deg, #F7F8F8 0%, #DCDFDF 100%)'
              : 'linear-gradient(180deg, #404848 0%, #868B8B 100%)',
          }}
          className={`cursor-pointer min-w-[144px] px-3 h-[38px] flex items-center justify-center
            ${index === 0 ? 'rounded-l-lg' : ''}
            ${index === tabs.length - 1 ? 'rounded-r-lg' : ''}
          `}
        >
          <p
            className={`text-center font-[500] text-[13px] m-0 ${
              activeView === tab.key ? 'text-white' : 'text-[#262D2D]'
            }`}
          >
            {tab.title}
          </p>
        </div>
      ))}
    </div>
  );
}

export default NetworkToggle;
