import React, { useState, useEffect } from 'react';
import {
  Modal,
  Box,
  Typography,
  TextField,
  Button,
  IconButton,
  Alert,
  CircularProgress,
  Grid,
} from '@mui/material';
import { Close as CloseIcon } from '@mui/icons-material';
import API from 'api/src/lib/api';
import { NetworkInviteRequest, NetworkInviteResponse } from 'types/networks-types';

interface NetworkInviteModalProps {
  open: boolean;
  onClose: () => void;
  userFullName: string;
}

const NetworkInviteModal: React.FC<NetworkInviteModalProps> = ({
  open,
  onClose,
  userFullName,
}) => {
  const [contactName, setContactName] = useState('');
  const [email, setEmail] = useState('');
  const [fromName, setFromName] = useState(userFullName);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Update fromName when userFullName prop changes
  useEffect(() => {
    if (userFullName && !fromName) {
      setFromName(userFullName);
    }
  }, [userFullName]);

  // Email validation regex
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  // Form validation
  const isFormValid = contactName.trim() && email.trim() && fromName.trim() && emailRegex.test(email);

  const handleSubmit = async () => {
    if (!isFormValid) return;

    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const inviteRequest: NetworkInviteRequest = {
        contact: contactName.trim(),
        email: email.trim(),
        from: fromName.trim(),
      };

      console.log('Sending invitation:', inviteRequest);

      const response: NetworkInviteResponse = await API.NETWORKS.inviteToNetwork(inviteRequest);

      console.log('Invitation response:', response);
      console.log('Response status:', response.status);

      // Check for successful status codes (200, 201, 202)
      if (response.status === 200 || response.status === 201 || response.status === 202 || response.status === undefined || response.status === null) {
        setSuccess(`Invitation sent successfully to ${email}`);

        // Clear form and close modal after 2 seconds
        setTimeout(() => {
          setContactName('');
          setEmail('');
          setFromName(userFullName); // Reset to default
          setSuccess('');
          onClose();
        }, 2000);
      } else {
        setError(response.message || 'Failed to send invitation');
      }
    } catch (error: any) {
      console.error('Error sending invitation:', error);
      
      // Extract meaningful error message
      let errorMessage = 'Failed to send invitation. Please try again.';
      
      if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error?.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error?.message) {
        errorMessage = error.message;
      }
      
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      setContactName('');
      setEmail('');
      setFromName(userFullName); // Reset to default
      setError('');
      setSuccess('');
      onClose();
    }
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && isFormValid && !loading) {
      handleSubmit();
    }
  };

  return (
    <Modal
      open={open}
      onClose={handleClose}
      aria-labelledby="network-invite-modal-title"
      aria-describedby="network-invite-modal-description"
    >
      <Box
        sx={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: { xs: '90%', sm: 500 },
          bgcolor: 'background.paper',
          borderRadius: 2,
          boxShadow: 24,
          p: 4,
          maxHeight: '90vh',
          overflow: 'auto',
        }}
      >
        {/* Header */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography id="network-invite-modal-title" variant="h5" component="h2" fontWeight="bold">
            Invite to Network
          </Typography>
          <IconButton
            onClick={handleClose}
            disabled={loading}
            sx={{ color: 'grey.500' }}
          >
            <CloseIcon />
          </IconButton>
        </Box>

        {/* Success/Error Messages */}
        {success && (
          <Alert severity="success" sx={{ mb: 3 }}>
            {success}
          </Alert>
        )}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Form */}
        <Grid container spacing={3}>
          {/* Contact Name */}
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Contact Name"
              value={contactName}
              onChange={(e) => setContactName(e.target.value)}
              onKeyPress={handleKeyPress}
              disabled={loading}
              required
              placeholder="Enter the invitee's full name"
              error={contactName.length > 0 && !contactName.trim()}
              helperText={contactName.length > 0 && !contactName.trim() ? 'Contact name is required' : ''}
            />
          </Grid>

          {/* Email */}
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Send To"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              onKeyPress={handleKeyPress}
              disabled={loading}
              required
              placeholder="Enter the recipient's email address"
              error={email.length > 0 && !emailRegex.test(email)}
              helperText={email.length > 0 && !emailRegex.test(email) ? 'Please enter a valid email address' : ''}
            />
          </Grid>

          {/* From (Editable) */}
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="From"
              value={fromName}
              onChange={(e) => setFromName(e.target.value)}
              onKeyPress={handleKeyPress}
              disabled={loading}
              required
              placeholder="Enter your name or organization"
              error={fromName.length > 0 && !fromName.trim()}
              helperText={fromName.length > 0 && !fromName.trim() ? 'From name is required' : ''}
            />
          </Grid>
        </Grid>

        {/* Action Buttons */}
        <Box display="flex" justifyContent="flex-end" gap={2} mt={4}>
          <Button
            variant="outlined"
            onClick={handleClose}
            disabled={loading}
            sx={{ borderColor: '#ea7200', color: '#ea7200' }}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={handleSubmit}
            disabled={!isFormValid || loading}
            startIcon={loading ? <CircularProgress size={20} color="inherit" /> : null}
            sx={{
              backgroundColor: '#ea7200',
              color: 'white',
              '&:hover': {
                backgroundColor: '#d65a00',
              },
              '&:disabled': {
                opacity: 0.6,
              },
            }}
          >
            {loading ? 'Sending...' : 'Send Invitation'}
          </Button>
        </Box>
      </Box>
    </Modal>
  );
};

export default NetworkInviteModal;
