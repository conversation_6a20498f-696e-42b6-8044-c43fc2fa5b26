import React, { useState, useEffect, useCallback } from 'react';
import {
  Modal,
  Box,
  Typography,
  TextField,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Button,
  CircularProgress,
  Divider,
} from '@mui/material';
import { Search as SearchIcon, Person as PersonIcon } from '@mui/icons-material';
import API from 'api/src/lib/api';
import { MemberSearchResponse, MemberSearchItem } from 'types/networks-types';

interface MemberSelectionModalProps {
  open: boolean;
  onClose: () => void;
  onSelect: (member: MemberSearchItem) => void;
  orgId?: string;
}

const modalStyle = {
  position: 'absolute' as 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: { xs: '90%', sm: 600 },
  maxHeight: '80vh',
  bgcolor: 'background.paper',
  borderRadius: 2,
  boxShadow: 24,
  p: 0,
  display: 'flex',
  flexDirection: 'column',
};

function MemberSelectionModal({ open, onClose, onSelect, orgId }: MemberSelectionModalProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<MemberSearchItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);

  // Debounced search function
  const debounceSearch = useCallback(
    (() => {
      let timeoutId: NodeJS.Timeout;
      return (query: string) => {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
          if (query.length >= 2) {
            performSearch(query);
          } else {
            setSearchResults([]);
            setHasSearched(false);
          }
        }, 400); // 400ms debounce delay
      };
    })(),
    []
  );

  const performSearch = async (query: string) => {
    if (query.length < 2) return;

    setLoading(true);
    setHasSearched(true);

    try {
      const response = await API.MEMBERS.searchMembers(query, orgId);
      const memberData: MemberSearchResponse = response.data || response;
      setSearchResults(memberData.items || []);
    } catch (error) {
      console.error('Error searching members:', error);
      setSearchResults([]);
    } finally {
      setLoading(false);
    }
  };

  // Handle search input change
  useEffect(() => {
    debounceSearch(searchQuery);
  }, [searchQuery, debounceSearch]);

  // Reset state when modal opens/closes
  useEffect(() => {
    if (open) {
      setSearchQuery('');
      setSearchResults([]);
      setHasSearched(false);
      setLoading(false);
    }
  }, [open]);

  const handleMemberSelect = (member: MemberSearchItem) => {
    onSelect(member);
    onClose();
  };

  const getProfileImage = (member: MemberSearchItem) => {
    if (member.attachments && member.attachments.length > 0) {
      // Find the first userProfile attachment since MemberSearchItem attachments don't have createdAt
      const profileAttachment = member.attachments.find(attachment => attachment.kind === 'userProfile');
      return profileAttachment?.url || null;
    }
    return null;
  };

  return (
    <Modal open={open} onClose={onClose}>
      <Box sx={modalStyle}>
        {/* Header */}
        <Box sx={{ p: 3, borderBottom: '1px solid #e0e0e0' }}>
          <Typography variant="h6" component="h2" sx={{ fontWeight: 600, color: '#001018' }}>
            Select a Member
          </Typography>
        </Box>

        {/* Search Input */}
        <Box sx={{ p: 3, borderBottom: '1px solid #e0e0e0' }}>
          <TextField
            fullWidth
            placeholder="Search members by name or email..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: <SearchIcon sx={{ color: '#666', mr: 1 }} />,
            }}
            variant="outlined"
            size="medium"
          />
          {searchQuery.length > 0 && searchQuery.length < 2 && (
            <Typography variant="caption" sx={{ color: '#666', mt: 1, display: 'block' }}>
              Type at least 2 characters to search
            </Typography>
          )}
        </Box>

        {/* Search Results */}
        <Box sx={{ flex: 1, overflow: 'auto', minHeight: 200, maxHeight: 400 }}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 4 }}>
              <CircularProgress size={24} />
              <Typography sx={{ ml: 2, color: '#666' }}>Searching members...</Typography>
            </Box>
          ) : hasSearched && searchResults.length === 0 ? (
            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', py: 4 }}>
              <PersonIcon sx={{ fontSize: 48, color: '#ccc', mb: 2 }} />
              <Typography variant="body1" sx={{ color: '#666', textAlign: 'center' }}>
                No members found for "{searchQuery}"
              </Typography>
              <Typography variant="body2" sx={{ color: '#999', textAlign: 'center', mt: 1 }}>
                Try searching with a different name or email
              </Typography>
            </Box>
          ) : searchResults.length > 0 ? (
            <List sx={{ p: 0 }}>
              {searchResults.map((member, index) => (
                <React.Fragment key={member.id}>
                  <ListItem
                    onClick={() => handleMemberSelect(member)}
                    sx={{
                      py: 2,
                      px: 3,
                      '&:hover': {
                        backgroundColor: '#f5f5f5',
                      },
                    }}
                  >
                    <ListItemAvatar>
                      <Avatar
                        src={getProfileImage(member) || undefined}
                        sx={{ width: 40, height: 40 }}
                      >
                        {!getProfileImage(member) && (
                          <PersonIcon />
                        )}
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={
                        <Typography sx={{ fontWeight: 500, color: '#001018' }}>
                          {member.firstName} {member.lastName}
                        </Typography>
                      }
                      secondary={
                        <Typography sx={{ color: '#666', fontSize: '14px' }}>
                          {member.email}
                        </Typography>
                      }
                    />
                  </ListItem>
                  {index < searchResults.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          ) : (
            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', py: 4 }}>
              <SearchIcon sx={{ fontSize: 48, color: '#ccc', mb: 2 }} />
              <Typography variant="body1" sx={{ color: '#666', textAlign: 'center' }}>
                Search for members
              </Typography>
              <Typography variant="body2" sx={{ color: '#999', textAlign: 'center', mt: 1 }}>
                Enter a name or email to find members
              </Typography>
            </Box>
          )}
        </Box>

        {/* Footer */}
        <Box sx={{ p: 3, borderTop: '1px solid #e0e0e0', display: 'flex', justifyContent: 'flex-end' }}>
          <Button
            onClick={onClose}
            variant="outlined"
            sx={{
              textTransform: 'none',
              borderColor: '#ea7200',
              color: '#ea7200',
              '&:hover': {
                borderColor: '#d66600',
                backgroundColor: '#fff5f0',
              },
            }}
          >
            Cancel
          </Button>
        </Box>
      </Box>
    </Modal>
  );
}

export default MemberSelectionModal;
