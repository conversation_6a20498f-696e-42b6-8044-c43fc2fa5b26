/* eslint-disable react/no-array-index-key */
import React from 'react';
import {
  FormControl, InputLabel, MenuItem, Select,
} from '@mui/material';
import { styled } from '@mui/system';

const StyledFormControl = styled(FormControl)({
  backgroundColor: 'white',
});

const StyledInputLabel = styled(InputLabel)({
  backgroundColor: 'transparent',
  '&.Mui-focused': {
    backgroundColor: 'transparent',
  },
  '&.MuiInputLabel-shrink': {
    backgroundColor: 'transparent',
  },
});

const StyledSelect = styled(Select)({
  backgroundColor: 'white',
  borderRadius: '8px',
  border: '1px solid #D5DCE2',
  height: '56px',
  '&.Mui-disabled': {
    backgroundColor: '#E6E8EB', // optional, to show disabled look
    color: '#A0A0A0',
  },
  // Remove the underline pseudo-elements for disabled
  '&.Mui-disabled::before, &.Mui-disabled::after': {
    borderBottom: 'none !important',
  },
  '& .MuiFilledInput-root': {
    backgroundColor: 'white',
    border: 'none',
    '&:hover': { backgroundColor: 'white' },
    '&.Mui-disabled': {
      backgroundColor: '#E6E8EB',
      color: '#A0A0A0',
    },
    '&.Mui-disabled:before, &.Mui-disabled:after': {
      borderBottom: 'none !important',
    },
  },
  '& .MuiFilledInput-underline': {
    '&:before, &:after': { borderBottom: 'none' },
    '&:hover:not(.Mui-disabled):before': { borderBottom: 'none' },
  },
});

interface SelectBoxProps {
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  minWidth?: number | string;
  items?: {
    key: string;
    title: string;
  }[];
  keyVal: string;
  label: string;
  defaultValue?: string;
  labelStyle?: React.CSSProperties;
  renderValue?: (selected: any) => React.ReactNode; // ✅ added
  disabled?: boolean;
}

function SelectBox({
  onChange,
  label,
  keyVal,
  items = [],
  minWidth = '100%',
  defaultValue = '',
  labelStyle,
  renderValue, // ✅ destructure it
  disabled,
}: SelectBoxProps) {
  return (
    <StyledFormControl className="w-5" variant="filled" sx={{ minWidth }}>
      <StyledInputLabel id={`select-input-label-${keyVal}`}>
        {label}
      </StyledInputLabel>
      <StyledSelect
        name={keyVal}
        style={{
          textTransform: 'capitalize',
          fontSize: '0.9em',
          ...labelStyle,
        }}
        className="first-letter:uppercase ml-[-1px]"
        value={defaultValue}
        labelId={`select-input-label-${keyVal}`}
        id={`select-input-${keyVal}`}
        onChange={(e) => {
          if (onChange) onChange(e as any);
        }}
        renderValue={renderValue} // ✅ forward to MUI Select
        disabled={disabled}
      >
        {items?.map((item) => (
          <MenuItem key={item?.key} value={item.key?.toLowerCase()}>
            <span className="first-letter:uppercase">{item.title}</span>
          </MenuItem>
        ))}
      </StyledSelect>
    </StyledFormControl>
  );
}

export default SelectBox;
