/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useContext, useEffect, useState } from 'react';
import { Drawer, But<PERSON>, Checkbox, TextField } from '@mui/material';
import Check from '@mui/icons-material/Check';
import { Close } from '@mui/icons-material';
import MainLayout from 'layouts/main-layout';
import MemberInnerPageHeader from 'components/inner-page-header/member';
import { ConstantsContext } from 'contexts/constant-context/constant-context';
import { HouseholdTag } from 'types/household-types';

interface HouseholdTagsDrawerProps {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  handleSave: (tags: Pick<HouseholdTag, 'name' | 'key' | 'color'>[]) => void;
  existingTags: HouseholdTag[];
}

function HouseholdTagsDrawer({ open, setOpen, handleSave, existingTags }: HouseholdTagsDrawerProps) {
  const constants = useContext(ConstantsContext);
  const { memberTags } = constants || {};

  const [selected, setSelected] = useState<Pick<HouseholdTag, 'name' | 'key' | 'color'>[]>([]);
  const [newTag, setNewTag] = useState<{ name: string; key: string; color: string }>({ name: '', key: '', color: '#888888' });

  const handleClose = () => setOpen(false);
  const saveTags = () => {
    handleSave(selected);
    handleClose();
  };

  function toggleTag(key: string, name: string, color: string) {
    setSelected((prev) => (prev.some((t) => t.key === key)
      ? prev.filter((t) => t.key !== key)
      : [...prev, { key, name, color }]));
  }

  function addNewTag() {
    if (!newTag.name || !newTag.key) return;
    setSelected((prev) => (prev.some((t) => t.key === newTag.key) ? prev : [...prev, newTag]));
    // Keep the form but clear only the name for quick multiples
    setNewTag((p) => ({ ...p, name: '' }));
  }

  useEffect(() => {
    setSelected(existingTags.map(({ key, name, color }) => ({ key, name, color })));
  }, [existingTags]);

  return (
    <Drawer
      sx={{ '& .MuiDrawer-paper': { height: '100%', width: '100%' } }}
      anchor="bottom"
      open={open}
      onClose={handleClose}
    >
      <MainLayout buttonProps={{ addButton: false }}>
        <div className="pt-6" />
        <MemberInnerPageHeader
          title="Households"
          fullName="Add Household"
          updateLabel="Select Tags"
          add={false}
          backRoute="/households"
          btnName="Save"
          callback={handleClose}
          rightSide={(
            <div className="flex flex-row gap-2 items-center">
              <Button onClick={handleClose} component="label" className="rounded-md m-0 p-0 px-5 h-[2.75rem] bg-[#F7F8F8] hover:bg-[#f0f0f0] text-[#E42B57] mr-3 min-w-[100px] transition-none">
                <Close style={{ fontSize: 18, marginRight: 8 }} />
                Cancel
              </Button>
              <Button onClick={saveTags} style={{ background: 'linear-gradient(to bottom, #FD8205, #E97100)', minWidth: '100px' }} variant="contained" className="rounded-md h-11 p-3 font-normal text-sm">
                <Check style={{ fontSize: 18, marginRight: 8 }} />
                <span className="text-center font-semibold">Save</span>
              </Button>
            </div>
          )}
        />

        <div className="pl-5 pr-5">
          <p className="text-lg font-[500]">Tag Library</p>
          {memberTags?.map((tag: any) => (
            // eslint-disable-next-line jsx-a11y/label-has-associated-control
            <label key={tag.key} className="grid grid-flow-col auto-cols-max items-center">
              <Checkbox
                checked={selected.some((x) => x.key === tag.key)}
                name={tag.name}
                onChange={() => toggleTag(tag.key, tag.title, tag.color)}
              />
              <p className="font-[500] text-sm m-0">{tag.title}</p>
            </label>
          ))}

{/* Great feature to add later */}
          {/* <div className="mt-6">
            <p className="text-md font-[500] mb-2">Create New Tag</p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3 items-center">
              <TextField label="Name" variant="filled" size="small" value={newTag.name} onChange={(e) => setNewTag((p) => ({ ...p, name: e.target.value }))} />
              <TextField label="Key" helperText="Unique key (e.g., slug)" variant="filled" size="small" value={newTag.key} onChange={(e) => setNewTag((p) => ({ ...p, key: e.target.value }))} />
              <TextField label="Color" variant="filled" size="small" value={newTag.color} onChange={(e) => setNewTag((p) => ({ ...p, color: e.target.value }))} />
            </div>
            <div className="mt-3">
              <Button onClick={addNewTag} variant="outlined" className="rounded-md h-9">+ Add Tag to Selection</Button>
            </div>
          </div> */}
        </div>
      </MainLayout>
    </Drawer>
  );
}

export default HouseholdTagsDrawer;

