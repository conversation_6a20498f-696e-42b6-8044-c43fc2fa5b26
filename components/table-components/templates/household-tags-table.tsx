/* eslint-disable max-len */
/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable import/no-cycle */
/* eslint-disable no-case-declarations */
import ActionContainer from 'components/action-container/action-container';
import SafeDate from 'components/date-time/SafeDate';
import { HouseholdTag } from 'types/household-types';
import { TableRenderProps } from '../render';

const pStyle = 'text-[13px]  p-0 m-0  text-[#001018] first-letter:uppercase';
function HouseholdTagsTable(props: TableRenderProps<HouseholdTag>) {
  const {
    data: {
      name, color, updatedAt, id,
    },
    children,
    value,
    sKey,
    handleDelete,
  } = props;

  switch (sKey) {
    case 'title':
      return (
        <div
          key={name}
          className="grid grid-flow-col cursor-pointer items-center justify-start w-full"
        >
          {children}
          <div
            className="w-3 h-3 rounded-full mr-3"
            style={{ backgroundColor: color || 'rgb(215, 38, 56)', background: color || 'rgb(215, 38, 56)' }}
          />
          <p className="font-[500] pr-1 text-[15px] truncate">{name}</p>
        </div>
      );
    case 'updatedAt':
      return <p className={pStyle}><SafeDate date={updatedAt} /></p>;
    case 'actions':
      return (
        <div className="flex justify-center items-center w-[50px]">
          <ActionContainer
            deleteLabel="Remove Tag"
            onDelete={handleDelete ? () => handleDelete({ id, name } as any) : undefined}
            editLabel=""
          />
        </div>
      );
    default:
      return Array.isArray(value) ? value.join(', ') : (value as any);
  }
}

export default HouseholdTagsTable;

