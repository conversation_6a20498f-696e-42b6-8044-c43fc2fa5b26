/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable import/no-cycle */
/* eslint-disable no-case-declarations */
import { ConnectedNetwork } from 'types/networks-types';
import { useRouter } from 'next/router';
import { TableRenderProps } from '../render';

const pStyle = 'text-[13px] p-0 m-0 text-[#001018] first-letter:uppercase';
const pStyleEllipsis = 'text-[13px] p-0 m-0 text-[#001018] first-letter:uppercase overflow-hidden';

function MainConnectedNetworkTable(props: TableRenderProps<ConnectedNetwork>) {
  const router = useRouter();
  const {
    data: {
      id, name, desc, addresses, phones,
    },
    children,
    value,
    sKey,
  } = props;

  switch (sKey) {
    case 'title':
      return (
        <div className="grid grid-flow-col items-center justify-start">
          {children}
          <p
            className="w-full text-[#001018] font-[500] cursor-pointer hover:text-blue-600"
            onClick={() => router.push(`/networks/connected/${id}`)}
          >
            {name}
          </p>
        </div>
      );
    case 'desc':
      return (
        <p
          className={pStyleEllipsis}
          style={{
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
          }}
        >
          {desc || 'N/A'}
        </p>
      );
    case 'address':
      return (
        addresses.length === 0 ? (
          <p className={pStyle}>
            N/A
          </p>
        ) : (
          <p
            className={pStyleEllipsis}
            style={{
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            }}
          >
            {addresses[0]?.address}
          </p>
        )
      );
    case 'phones':
      return (
        phones.length === 0 ? (
          <p className={pStyle}>
            N/A
          </p>
        ) : (
          <p
            className={pStyleEllipsis}
            style={{
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            }}
          >
            {phones.map((phone) => phone.number).join(', ')}
          </p>
        )
      );
    default:
      return Array.isArray(value) ? value.join(', ') : value;
  }
}

export default MainConnectedNetworkTable;
