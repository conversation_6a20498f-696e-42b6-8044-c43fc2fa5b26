/* eslint-disable max-len */
/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable import/no-cycle */
/* eslint-disable no-case-declarations */
import ActionContainer from 'components/action-container/action-container';
import { capitalizeWords } from 'utils/helper';
import API from 'api/src/lib/api';
import router from 'next/router';
import useRefreshSSR from 'hooks/use-refresh-ssr';
import SafeDate from 'components/date-time/SafeDate';
import { useModal } from 'contexts/modal-context/modal-context';
import { TableRenderProps } from '../render';

const pStyle = 'text-[15px] p-0 m-0 py-3 text-[#262D2D] font-[500]';
function MemberProgramsTable(props: TableRenderProps<any>) {
  const {
    data: {
      programType,
      startDate,
      endDate,
      reviewFrequencyDays,
      reviewPeriods,
      status,
      id,
    },
    children,
    value,
    sKey,
    handleDelete,
  } = props;
  const { refresh } = useRefreshSSR();
  const { showModal } = useModal();

  const statusColors = {
    active: '#008390',
    complete: '#6E6E6E',
  };

  // const handleDeleteProgram = async (id: string) => {
  //   showModal(
  //     'Warning',
  //     'Are you sure you want to remove this program?',
  //     async () => {
  //       const response = await API.MEMBERS.deleteMemberProgram(id);
  //       if (response) {
  //         refresh();
  //       }
  //     },
  //   );
  // };

  switch (sKey) {
    case 'title':
      return (
        <div
          key={id}
          className="grid grid-flow-col cursor-pointer items-center justify-start w-full"
          onClick={() => router.push(`/members/programs/${id}/detail/${id}`)}
        >
          {children}
          <p className={pStyle}>{capitalizeWords(programType, true)}</p>
        </div>
      );
    case 'enrollmentDate':
      return (
        <p className={pStyle}>
          <SafeDate date={startDate} />
        </p>
      );
    case 'endDate':
      return (
        <p className={pStyle}>
          <SafeDate date={endDate} />
        </p>
      );
    case 'reviewFrequency':
      return (
        <p className={pStyle}>
          {reviewFrequencyDays}
          {' '}
          days
        </p>
      );
    case 'status':
      return <p className={pStyle} style={{ color: statusColors[status as keyof typeof statusColors] }}>{capitalizeWords(status, true) || '-'}</p>;
    case 'actions':
      return (
        <div className="flex justify-center items-center w-[50px]">
          {/* <ActionContainer
            deleteLabel="Delete Program"
            onDelete={() => handleDeleteProgram(id)}
            // editLabel="Edit Insurance"
            // onEdit={() => router.push(`/members/insurance/${userId}/add-edit/${id}`)}
          /> */}
        </div>
      );
    default:
      return Array.isArray(value) ? value.join(', ') : value;
  }
}

export default MemberProgramsTable;
