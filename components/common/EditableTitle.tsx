import { useState } from 'react';
import { Input } from '@mui/material';
import EditIcon from 'public/icons/EditIcon';

function EditableTitle({
  initialTitle,
  onSave,
  size,
  fontWeight,
  fontStyle,
  editIconClassName,
}: {
  initialTitle: string;
  onSave: (newTitle: string) => void | Promise<void>;
  size?: number;
  fontWeight?: number;
  fontStyle?: string;
  editIconClassName?: string;
}) {
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [title, setTitle] = useState(initialTitle);
  const sizeString = size ? `${size}px` : '32px';

  const handleSaveTitle = async () => {
    const trimmedTitle = title.trim();

    // If the input is empty, revert to the initialTitle
    if (!trimmedTitle) {
      setTitle(initialTitle);
      setIsEditingTitle(false);
      return;
    }

    setIsEditingTitle(false);
    if (trimmedTitle !== initialTitle) {
      await onSave(trimmedTitle);
    }
  };

  const handleEditCarePlanTitle = () => {
    setIsEditingTitle(true);
  };

  return (
    <div className="flex items-center">
      {isEditingTitle ? (
        <Input
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          autoFocus
          onBlur={handleSaveTitle}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              handleSaveTitle();
            }
          }}
          disableUnderline
          sx={{
            fontSize: sizeString || '32px',
            fontWeight: 500,
            fontFamily: 'Open Runde, sans-serif',
            color: 'var(--dark-text)',
            padding: '4px 8px',
            borderRadius: '6px',
            backgroundColor: '#F5F5F5',
            minWidth: '400px',
            width: `max(200px, ${Math.max(title.length, 1)}ch)`,
          }}
        />
      ) : (
        <h1
          className={`text-[${sizeString || '32px'}] font-[${
            fontWeight || '500'
          }] ${
            !fontStyle && 'font-open-runde'
          } text-dark-text m-0 p-0 flex items-center`}
          style={{ fontFamily: fontStyle || 'Open Runde, sans-serif' }}
        >
          {title}
          <EditIcon
            stroke="#008390"
            size={size && size < 32 ? size * 1.65 : 32}
            onClick={handleEditCarePlanTitle}
            className={`ml-4 cursor-pointer p-1 ${editIconClassName}`}
          />
        </h1>
      )}
    </div>
  );
}

export default EditableTitle;
