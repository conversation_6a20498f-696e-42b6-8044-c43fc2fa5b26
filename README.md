# DonaWorkspace

<a alt="Nx logo" href="https://nx.dev" target="_blank" rel="noreferrer"><img src="https://raw.githubusercontent.com/nrwl/nx/master/images/nx-logo.png" width="45"></a>

✨ **This workspace has been generated by [Nx, a Smart, fast and extensible build system.](https://nx.dev)** ✨

## Development servers

Run `nx serve dona-workspace` for a dev server. Navigate to http://localhost:4200/. The app will automatically reload if you change any of the source files.

## Understand this workspace

Run `nx graph` to see a diagram of the dependencies of the projects.

## Remote caching

Run `npx nx connect-to-nx-cloud` to enable [remote caching](https://nx.app) and make CI faster.

## Further helpmainb

Visit the [Nx Documentation](https://nx.dev) to learn more.

## Linting

Run 2 commands prior to any PR releases to verify there are no type issues that will cause the github build action to fail.

`npx tsc --noEmit`

`npx nx run dona-workspace:build:production`