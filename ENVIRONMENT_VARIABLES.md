# Environment Variables

This document lists the environment variables required for the application.

## Background Check Integration

The offender search functionality now uses the existing API infrastructure and authentication system. No additional environment variables are required for background checks - it uses the same authentication as other API calls in the application.

## Existing Environment Variables

### NEXT_PUBLIC_MAPBOX_TOKEN
- **Description**: Mapbox API token for geocoding and mapping functionality
- **Example**: `pk.eyJ1IjoiYm91bG1wb3MiLCJhIjoiY2wzaDdmMXk3MTljbTNrcDhxMmRvczc0aSJ9.E8moVfuzANGeV5OaSAK8gg`
- **Required**: Yes (for address geocoding)
- **Note**: Used for converting addresses to coordinates and reverse geocoding

## Setup Instructions

1. Create a `.env.local` file in the project root (if not already exists)
2. Add the required environment variables:

```bash
# Mapbox Configuration (if not already set)
NEXT_PUBLIC_MAPBOX_TOKEN=your-mapbox-token-here
```

3. Restart the development server for changes to take effect

## Authentication

The background check functionality uses the existing user authentication system:
- Uses the same `BASE_URL` as other API calls
- Uses the logged-in user's token via `TOKEN()` function
- No additional authentication configuration required

## Security Notes

- Never commit actual tokens or sensitive values to version control
- Use different tokens for development, staging, and production environments
- Background checks require user to be logged in with appropriate permissions
