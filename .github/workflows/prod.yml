name:  Build-And-Deploy-To-Prod
on:
  # Triggers the workflow on push to matching branches
   workflow_dispatch:

env:
  duplo_host: https://duplo.duploapps.dona.health
  duplo_token: "${{ secrets.DUPLO_TOKEN }}"
  SERVICE_NAME: dona-workspace
  TENANT_NAME: prod
  BACKEND_URL: https://donahealth-prod.duploapps.dona.health

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      # Set up for docker build
      - name: Get AWS credentials
        uses: duplocloud/ghactions-aws-jit@master
        with:
          tenant: default
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1
      - name: Docker Build and Push
        uses: docker/build-push-action@v2
        with:
          context: .
          push: true
          file: ./Dockerfile
          build-args: |
            BASE_URL=${{ env.BACKEND_URL }}
          tags: |
            ${{ steps.login-ecr.outputs.registry }}/${{ env.SERVICE_NAME }}:${{ github.sha }}
    # This part is important - it will be used by the deploy job
    outputs:
      image: "${{ steps.login-ecr.outputs.registry }}/${{ env.SERVICE_NAME }}:${{ github.sha }}"
  deploy:
    runs-on: ubuntu-latest
    needs:
      - build
    steps:
      # Update the backend service to use the new image.
      - name: Deploy
        uses: duplocloud/ghactions-service-update@master
        with:
          tenant: "${{ env.TENANT_NAME }}"
          services: |-
            [
              { "Name": "${{ env.SERVICE_NAME }}", "Image": "${{ needs.build.outputs.image }}" }
            ]
                        
  notify_slack:
    runs-on: ubuntu-latest
    needs: deploy
    if: always()  # Ensure it runs even if the deploy fails
    steps:
      - name: Send Slack Notification
        uses: slackapi/slack-github-action@v1
        with:
          channel-id: "${{ secrets.SLACK_CHANNEL_ID }}"
          slack-message: >
            *Build-Deploy-To-Dev Workflow Status:* 
            ${{ needs.deploy.result == 'success' && '✅ Deployment Successful!' || '❌ Deployment Failed!' }} 
            - *Branch:* `production`
        env:
          SLACK_BOT_TOKEN: "${{ secrets.SLACK_BOT_TOKEN }}"
