import { Add } from '@mui/icons-material';
import { Button } from '@mui/material';
import API from 'api/src/lib/api';
import { Network, ConnectedNetwork, ConnectedNetworksResponse } from 'types/networks-types';
import Search from 'components/search/search';
import CustomTable from 'components/table-components/table';
import NetworkToggle from 'components/networks/network-toggle';
import NetworkInviteModal from 'components/modals/network-invite-modal';
import { parse } from 'cookie';
import MainLayout from 'layouts/main-layout';
import { GetServerSidePropsContext } from 'next';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';

interface PageInfo {
  per: number;
  total: number;
  page: number;
}

interface NetworksProps {
  networks: Network[];
  pageInfo: PageInfo;
  userFullName?: string;
}

function Networks({
  networks,
  pageInfo,
  userFullName = 'User'
}: NetworksProps) {
  const [networkData, setNetworkData] = useState<Network[]>([]);
  const [connectedNetworkData, setConnectedNetworkData] = useState<ConnectedNetwork[]>([]);
  const [activeView, setActiveView] = useState<'networks' | 'connected'>('networks');
  const [loadingConnected, setLoadingConnected] = useState(false);
  const [inviteModalOpen, setInviteModalOpen] = useState(false);
  const router = useRouter();
  const { sortBy = 'createdAt', sortDirection = 'desc', view } = router.query;

  useEffect(() => {
    setNetworkData(networks);
  }, [networks]);

  useEffect(() => {
    if (view === 'connected') {
      setActiveView('connected');
      if (connectedNetworkData.length === 0) {
        fetchConnectedNetworks();
      }
    }
  }, [view]);

  const fetchConnectedNetworks = async () => {
    setLoadingConnected(true);
    try {
      const response: ConnectedNetworksResponse = await API.NETWORKS.fetchConnectedNetworks();
      setConnectedNetworkData(response.data.org.children);
    } catch (error) {
      console.error('Failed to fetch connected networks:', error);
      setConnectedNetworkData([]);
    } finally {
      setLoadingConnected(false);
    }
  };

  const handleToggle = (view: 'networks' | 'connected') => {
    setActiveView(view);

    // Update URL with view parameter
    const currentQuery = { ...router.query };
    if (view === 'connected') {
      currentQuery.view = 'connected';
    } else {
      delete currentQuery.view;
    }

    router.push({
      pathname: router.pathname,
      query: currentQuery,
    }, undefined, { shallow: true });

    if (view === 'connected' && connectedNetworkData.length === 0) {
      fetchConnectedNetworks();
    }
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const searchTerm = e.target.value;
    router.push({
      pathname: '/networks',
      query: {
        ...router.query,
        search: searchTerm || undefined,
        page: 1,
      },
    });
  };

  return (
    <MainLayout
      buttonProps={{
        addButton: false,
        addButtonOnClick: () => router.push('/networks/add-edit/add'),
        addButtonLabel: 'Add Network',
      }}
    >
      <div style={{ gridTemplateColumns: 'min-content 1fr' }}>
        <div className="pl-[15px] pb-0 pt-[15px] max-h-screen grid grid-flow-col auto-cols-auto items-center overflow-auto pr-5">
          <div className="grid grid-flow-row gap-3">
            <h1 className="font-[600] text-[21px] p-0 m-0">
              {activeView === 'networks' ? 'Networks' : 'Connected Networks'}
              <span className="font-[300]">
                {' '}
                (
                {activeView === 'networks' ? pageInfo.total : connectedNetworkData.length}
                )
              </span>
            </h1>
            <NetworkToggle activeView={activeView} onToggle={handleToggle} />
          </div>
          <div className="justify-self-end grid grid-flow-col auto-cols-max gap-2 items-center">
            {activeView === 'networks' && (
              <Search placeholder="Search Network" onChange={handleSearch} />
            )}
            {activeView === 'networks' && (
              <Button
                style={{ fontWeight: 500 }}
                variant="contained"
                onClick={() => router.push('/networks/add-edit/add')}
                className="rounded-md font-[500] text-sm"
              >
                <Add style={{ color: 'white', fontSize: 21 }} />
                <span className="font-[500]">Add Network</span>
              </Button>
            )}
            
            {activeView === 'connected' && (
              <Button
                variant="contained"
                onClick={() => setInviteModalOpen(true)}
                sx={{
                  backgroundColor: '#ea7200',
                  color: 'white',
                  textTransform: 'none',
                  fontWeight: 500,
                  borderRadius: '6px',
                  px: 3,
                  py: 1,
                  '&:hover': {
                    backgroundColor: '#d65a00',
                  },
                }}
              >
                Invite
              </Button>
            )}
          </div>
        </div>

        {activeView === 'networks' ? (
          <CustomTable
            source={networkData}
            className="pr-1"
            variant="main-network-table"
            headCellStyle={{
              paddingLeft: '16px',
              paddingBottom: '4px',
            }}
            pageInfo={pageInfo}
            sortBy={sortBy as string}
            sortDirection={sortDirection as 'asc' | 'desc'}
            onPageChange={(newPage) => {
              router.push({
                pathname: '/networks',
                query: {
                  ...router.query,
                  page: newPage,
                  sortBy,
                  sortDirection,
                },
              });
            }}
            onSortChange={(newSortBy, newDirection) => {
              router.push({
                pathname: '/networks',
                query: {
                  ...router.query,
                  page: 1,
                  sortBy: newSortBy,
                  sortDirection: newDirection,
                },
              });
            }}
          />
        ) : (
          <div>
            {loadingConnected ? (
              <div className="flex justify-center items-center h-64">
                <p className="text-gray-500">Loading connected networks...</p>
              </div>
            ) : (
              <CustomTable
                source={connectedNetworkData}
                className="pr-1"
                variant="main-connected-network-table"
                thirdColumnStaticWidth={10}
                fourthColumnStaticWidth={10}
                headCellStyle={{
                  paddingLeft: '16px',
                  paddingBottom: '4px',
                }}
                tableCellStyle={{
                  paddingLeft: '16px',
                  paddingBottom: '10px',
                  paddingTop: '10px'
                }}
                pageInfo={{ per: connectedNetworkData.length, total: connectedNetworkData.length, page: 1 }}
              />
            )}
          </div>
        )}
      </div>

      {/* Network Invite Modal */}
      <NetworkInviteModal
        open={inviteModalOpen}
        onClose={() => setInviteModalOpen(false)}
        userFullName={userFullName}
      />
    </MainLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  const { req, query } = context;
  let token;
  let orgId;
  let userId;
  const page = parseInt((query.page as string) || '1', 10);
  const sortBy = (query.sortBy as string) || 'createdAt';
  const sortDirection = (query.sortDirection as string) || 'desc';
  const search = (query.search as string) || '';
  const pageSize = 15;

  if (req.headers.cookie) {
    const cookies = parse(req.headers.cookie);
    orgId = cookies.orgID;
    token = cookies.token;
    userId = cookies.userID;
  }

  const networks: any = token && orgId
    ? await API.NETWORKS.fetchNetworks(
      token,
      orgId,
      pageSize,
      page,
      sortBy,
      sortDirection,
      search,
    )
    : { items: [], metadata: { per: 0, total: 0, page: 0 } };

  // Fetch user details for invitation modal
  let userFullName = 'User';

  try {
    if (userId && token && orgId) {
      const userResponse = await API.USER.getUser(userId, token, orgId);
      const userData = userResponse?.data || userResponse;
      if (userData?.firstName && userData?.lastName) {
        userFullName = `${userData.firstName} ${userData.lastName}`;
      }
    }
  } catch (userError) {
    console.error('Error fetching user details:', userError);
    // Continue with default values
  }

  return {
    props: {
      networks: networks.items || [],
      pageInfo: networks.metadata || { per: 0, total: 0, page: 0 },
      userFullName,
    },
  };
}

export default Networks;
