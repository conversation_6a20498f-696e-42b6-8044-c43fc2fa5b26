import { GetServerSidePropsContext } from 'next';
import { useRouter } from 'next/router';
import { parse } from 'cookie';
import { Button } from '@mui/material';
import API from 'api/src/lib/api';
import MainLayout from 'layouts/main-layout';
import { ConnectedNetworkDetails, ConnectedNetworkDetailsResponse } from 'types/networks-types';

interface ConnectedNetworkDetailProps {
  networkDetails: ConnectedNetworkDetails | null;
  error?: string;
}

function ConnectedNetworkDetail({ networkDetails, error }: ConnectedNetworkDetailProps) {
  const router = useRouter();

  if (error) {
    return (
      <MainLayout buttonProps={{ addButton: false }}>
        <div className="p-8">
          <div className="mb-6">
            <p className="text-[21px] p-0 m-0 font-[500]">
              <span
                className="font-[300] cursor-pointer"
                onClick={() => router.push('/networks')}
              >
                Networks
              </span>
              <span className="font-[300]"> {'>'} </span>
              <span
                className="font-[300] cursor-pointer"
                onClick={() => router.push('/networks?view=connected')}
              >
                Connected Networks
              </span>
              <span className="font-[300]"> {'>'} </span>
              <span className="font-[500]">Error</span>
            </p>
          </div>
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-red-600 font-[500]">Error loading network details</p>
            <p className="text-red-500 text-sm mt-2">{error}</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  if (!networkDetails) {
    return (
      <MainLayout buttonProps={{ addButton: false }}>
        <div className="p-8">
          <div className="flex justify-center items-center h-64">
            <p className="text-gray-500">Loading network details...</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  const formatDateTime = (isoString: string) => {
    try {
      const date = new Date(isoString);
      return date.toLocaleString('en-US', {
        weekday: 'short',
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch {
      return 'Invalid date';
    }
  };

  return (
    <MainLayout buttonProps={{ addButton: false }}>
      <div className="p-8">
        {/* Breadcrumb Navigation */}
        <div className="mb-6">
          <p className="text-[21px] p-0 m-0 font-[500]">
            <span
              className="font-[300] cursor-pointer"
              onClick={() => router.push('/networks')}
            >
              Networks
            </span>
            <span className="font-[300]"> {'>'} </span>
            <span
              className="font-[300] cursor-pointer"
              onClick={() => router.push('/networks?view=connected')}
            >
              Connected Networks
            </span>
            <span className="font-[300]"> {'>'} </span>
            <span className="font-[500]">{networkDetails.name}</span>
          </p>
        </div>

        {/* Network Information Section */}
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6" style={{ paddingBottom: '0px' }}>
          <h1 className="text-2xl font-[600] text-[#001018] mb-4">{networkDetails.name}</h1>
           <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Addresses */}
            <div>
              <h3 className="text-sm font-[500] text-gray-600 mb-2">Address</h3>
              {networkDetails.addresses.length > 0 ? (
                <div className="space-y-1">
                  {networkDetails.addresses.map((addr, index) => (
                    <p key={index} className="text-[13px] text-[#001018]">
                      {addr.address}
                    </p>
                  ))}
                </div>
              ) : (
                <p className="text-[13px] text-gray-500">No address available</p>
              )}
            </div>

            {/* Phone Numbers */}
            <div>
              <h3 className="text-sm font-[500] text-gray-600 mb-2">Phone</h3>
              {networkDetails.phones.length > 0 ? (
                <div className="space-y-1">
                  {networkDetails.phones.map((phone, index) => (
                    <p key={index} className="text-[13px] text-[#001018]">
                      {phone.number}
                    </p>
                  ))}
                </div>
              ) : (
                <p className="text-[13px] text-gray-500">No phone available</p>
              )}
            </div>
          </div>
          {networkDetails.desc && (
            <div className="mb-4">
              <h3 className="text-sm font-[500] text-gray-600 mb-2">Description</h3>
              <p className="text-[13px] text-[#001018]">{networkDetails.desc}</p>
            </div>
          )}         
        </div>

        {/* Services & Appointments Section - Scrollable */}
        <div className="bg-white rounded-lg border border-gray-200 flex flex-col" style={{ height: 'calc(100vh - 400px)', minHeight: '400px' }}>
          <div className="p-6 border-b border-gray-200 flex-shrink-0" style={{ height: '60px' }}>
            <h2 className="text-xl font-[600] text-[#001018]">Services & Appointments</h2>
          </div>

          <div className="flex-1 overflow-y-auto p-6">
            {networkDetails.services.length > 0 ? (
              <div className="space-y-4">
                {networkDetails.services.map((service) => (
                  <div key={service.id} className="border border-gray-100 rounded-md p-4" style={{ paddingBottom: '0px', paddingTop: '5px' }}>
                    <div className="flex justify-between items-start">
                      <h3 className="text-[15px] font-[500] text-[#001018]" >{service.title}</h3>
                    </div>

                    {service.description && (
                      <p
                        className="text-[13px] text-[#001018] mb-2"
                        style={{
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                        }}
                      >
                        {service.description}
                      </p>
                    )}

                    <div className="flex justify-between items-end">
                      <div className="flex flex-col gap-1 text-[13px] text-gray-600">
                        {service.nextAvailableAppt && service.nextAvailableAppt.length > 0 && (
                        <div>
                          Next available: <span style={{ color: '#ea7200', fontWeight: 800 }}>{formatDateTime(service.nextAvailableAppt[0].startIso)}</span>
                        </div>
                        )}
                        <span>Duration: {service.durationMins} minutes</span>
                        <div>
                          Kind: <span className={`px-2 py-1 text-xs font-[500] flex-shrink-0 ${ service.kind === 'appointment'? 'text-blue-800': 'text-green-800'}`}>
                        {service.kind === 'appointment' ? 'Appointment' : 'Queue'}
                      </span>
                        </div>                        
                      </div>

                      <Button
  variant="contained"
  size="small"
  onClick={() =>
    router.push(
      `/networks/connected/${networkDetails.id}/book-appointment?serviceId=${service.id}`
    )
  }
  style={{
    backgroundColor: '#ea7200',
    color: 'white',
    textTransform: 'none',
    fontSize: '12px',
    padding: '6px 12px',
    borderRadius: '6px', // <- rounded corners
  }}
>
  Book Appointment
</Button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-[13px] text-gray-500">No services available</p>
            )}
          </div>
        </div>
      </div>
    </MainLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const { req, query } = context;
    const networkId = query.id as string;
    let token;
    let orgId;

    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);
      orgId = cookies.orgID;
      token = cookies.token;
    }

    if (!token || !orgId) {
      return {
        props: {
          networkDetails: null,
          error: 'Authentication required',
        },
      };
    }

    const response: ConnectedNetworkDetailsResponse = await API.NETWORKS.fetchConnectedNetworkDetails(
      networkId,
      token,
      orgId
    );

    return {
      props: {
        networkDetails: response.data.org,
        error: null,
      },
    };
  } catch (error: any) {
    console.error('Error fetching connected network details:', error);

    return {
      props: {
        networkDetails: null,
        error: error?.message || 'Failed to load network details',
      },
    };
  }
}

export default ConnectedNetworkDetail;
