import { GetServerSidePropsContext } from 'next';
import { useRouter } from 'next/router';
import { parse } from 'cookie';
import { useState, useEffect } from 'react';
import { Button, TextField, MenuItem, FormControl, InputLabel, Select, Grid, Typography, Paper, Box, Alert, CircularProgress } from '@mui/material';
import API from 'api/src/lib/api';
import MainLayout from 'layouts/main-layout';
import MemberSelectionModal from 'components/modals/member-selection-modal';
import { convertToEpoch, calculateEndEpoch } from 'utils/helper';
import {
  ConnectedNetworkDetails,
  NetworkOverviewResponse,
  AvailableTimesResponse,
  AvailableTimesRequest,
  MemberSearchItem,
  AppointmentBookingRequest,
  AppointmentBookingResponse
} from 'types/networks-types';

interface BookAppointmentProps {
  networkDetails: ConnectedNetworkDetails | null;
  networkOverview: NetworkOverviewResponse['data']['org'] | null;
  preSelectedServiceId?: string;
  error?: string;
}

function BookAppointment({ networkDetails, networkOverview, preSelectedServiceId, error }: BookAppointmentProps) {
  const router = useRouter();
  const [selectedService, setSelectedService] = useState<string>(preSelectedServiceId || '');
  const [selectedProvider, setSelectedProvider] = useState<string>('');
  const [selectedMember, setSelectedMember] = useState<MemberSearchItem | null>(null);
  const [selectedDate, setSelectedDate] = useState<string>('');
  const [selectedTime, setSelectedTime] = useState<string>('');
  const [additionalInfo, setAdditionalInfo] = useState<string>('');
  const [availableTimes, setAvailableTimes] = useState<string[]>([]);
  const [loadingTimes, setLoadingTimes] = useState(false);
  const [memberModalOpen, setMemberModalOpen] = useState(false);
  const [bookingLoading, setBookingLoading] = useState(false);
  const [bookingError, setBookingError] = useState<string>('');
  const [bookingSuccess, setBookingSuccess] = useState<string>('');

  // Format date for API (MM/DD/YYYY) - timezone safe
  const formatDateForAPI = (dateString: string) => {
    // Parse the date string directly to avoid timezone issues
    const [year, month, day] = dateString.split('-');
    return `${month}/${day}/${year}`;
  };

  // Format time for display
  const formatTimeForDisplay = (isoString: string) => {
    try {
      const date = new Date(isoString);
      return date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true,
      });
    } catch {
      return 'Invalid time';
    }
  };

  // Check if all required fields are selected
  const isFormValid = selectedService && selectedProvider && selectedMember && selectedDate && selectedTime;

  // Fetch available times when service, provider, and date are selected
  useEffect(() => {
    if (selectedService && selectedProvider && selectedDate && networkDetails) {
      setLoadingTimes(true);
      const formattedDate = formatDateForAPI(selectedDate);
      const requestData: AvailableTimesRequest = {
        org: networkDetails.id,
        service: selectedService,
        date: formattedDate
      };

      console.log('Date selected:', selectedDate);
      console.log('Date formatted for API:', formattedDate);
      console.log('Request data:', requestData);

      API.NETWORKS.fetchAvailableTimes(requestData)
        .then((response: AvailableTimesResponse) => {
          setAvailableTimes(response.data.org.service.availableTimesIso);
        })
        .catch((err) => {
          console.error('Error fetching available times:', err);
          setAvailableTimes([]);
        })
        .finally(() => {
          setLoadingTimes(false);
        });
    } else {
      setAvailableTimes([]);
    }
  }, [selectedService, selectedProvider, selectedDate, networkDetails]);

  // Handle appointment booking submission
  const handleBookAppointment = async () => {
    if (!isFormValid || !networkDetails) return;

    setBookingLoading(true);
    setBookingError('');
    setBookingSuccess('');

    try {
      // Get service details for duration
      const selectedServiceDetails = networkOverview?.services.find(s => s.id === selectedService);
      if (!selectedServiceDetails) {
        throw new Error('Service details not found');
      }

      // Convert selected time to epoch
      const startEpoch = convertToEpoch(selectedTime);
      const endEpoch = calculateEndEpoch(startEpoch, selectedServiceDetails.durationMins);

      // Prepare booking request
      const bookingRequest: AppointmentBookingRequest = {
        person: selectedMember.id,
        provider: selectedProvider,
        kind: 'appointment',
        org: networkDetails.id,
        service: selectedService,
        startEpoch,
        endEpoch,
        reason: additionalInfo || undefined,
      };

      console.log('Booking request:', bookingRequest);

      // Submit booking
      const response: AppointmentBookingResponse = await API.NETWORKS.bookAppointment(bookingRequest);

      console.log('Booking response:', response);
      console.log('Response data:', response.data);
      console.log('apptBook:', response.data?.apptBook);

      // Check if response has the expected structure
      if (!response.data || !response.data.apptBook) {
        throw new Error('Invalid response structure from booking API');
      }

      if (response.data.apptBook.success) {
        setBookingSuccess(`Appointment booked successfully! Appointment ID: ${response.data.apptBook.appointment.id}`);

        // Redirect after 3 seconds
        setTimeout(() => {
          router.push(`/networks/connected/${networkDetails.id}`);
        }, 3000);
      } else {
        const errors = response.data.apptBook.errors;
        setBookingError(errors.length > 0 ? errors.join(', ') : 'Failed to book appointment');
      }
    } catch (error: any) {
      console.error('Error booking appointment:', error);

      // Extract meaningful error message
      let errorMessage = 'Failed to book appointment. Please try again.';

      if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error?.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error?.response?.data) {
        errorMessage = JSON.stringify(error.response.data);
      } else if (error?.message) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      }

      setBookingError(errorMessage);
    } finally {
      setBookingLoading(false);
    }
  };

  if (error) {
    return (
      <MainLayout buttonProps={{ addButton: false }}>
        <div className="p-8">
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-red-600 font-[500]">Error loading booking page</p>
            <p className="text-red-500 text-sm mt-2">{error}</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  if (!networkDetails || !networkOverview) {
    return (
      <MainLayout buttonProps={{ addButton: false }}>
        <div className="p-8">
          <div className="flex justify-center items-center h-64">
            <p className="text-gray-500">Loading booking information...</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout buttonProps={{ addButton: false }}>
      <div className="p-8">
        {/* Breadcrumb Navigation */}
        <div className="mb-6">
          <p className="text-[21px] p-0 m-0 font-[500]">
            <span
              className="font-[300] cursor-pointer"
              onClick={() => router.push('/networks')}
            >
              Networks
            </span>
            <span className="font-[300]"> {'>'} </span>
            <span
              className="font-[300] cursor-pointer"
              onClick={() => router.push('/networks?view=connected')}
            >
              Connected Networks
            </span>
            <span className="font-[300]"> {'>'} </span>
            <span
              className="font-[300] cursor-pointer"
              onClick={() => router.push(`/networks/connected/${networkDetails.id}`)}
            >
              {networkDetails.name}
            </span>
            <span className="font-[300]"> {'>'} </span>
            <span className="font-[500]">Book Appointment</span>
          </p>
        </div>

        {/* Page Title */}
        <Typography variant="h5" className="mb-4 font-[600] text-[#001018]">
        Appointment Details
        </Typography>

        <Paper elevation={0} className="p-6">
          {/* Success/Error Messages */}
          {bookingSuccess && (
            <Alert severity="success" sx={{ mb: 3 }}>
              {bookingSuccess}
            </Alert>
          )}
          {bookingError && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {bookingError}
            </Alert>
          )}

          <Grid container spacing={3}>
            {/* Service Selection */}
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Service</InputLabel>
                <Select
                  value={selectedService}
                  label="Service"
                  onChange={(e) => setSelectedService(e.target.value)}
                  disabled={bookingLoading}
                >
                  {networkOverview.services.map((service) => (
                    <MenuItem key={service.id} value={service.id}>
                      {service.title} ({service.durationMins} mins)
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* Member Selection */}
            <Grid item xs={12} md={4}>
              <Button
                variant="outlined"
                fullWidth
                onClick={() => setMemberModalOpen(true)}
                disabled={bookingLoading}
                style={{
                  height: '56px',
                  justifyContent: 'flex-start',
                  textTransform: 'none',
                  color: selectedMember ? '#000' : '#666',
                  borderColor: '#ea7200',
                  opacity: bookingLoading ? 0.6 : 1,
                  borderRadius: '8px',
                }}
              >
                {selectedMember ? `${selectedMember.firstName} ${selectedMember.lastName}` : 'Select a Member'}
              </Button>
            </Grid>

            {/* Provider Selection */}
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Provider</InputLabel>
                <Select
                  value={selectedProvider}
                  label="Provider"
                  onChange={(e) => setSelectedProvider(e.target.value)}
                  disabled={!selectedService || bookingLoading}
                >
                  {networkOverview.providers.nodes.map((provider) => (
                    <MenuItem key={provider.id} value={provider.id}>
                      {provider.firstName} {provider.lastName}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>            

            {/* Date Selection */}
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Date"
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                InputLabelProps={{
                  shrink: true,
                }}
                disabled={!selectedService || !selectedProvider || bookingLoading}
                helperText={selectedDate ? `API will receive: ${formatDateForAPI(selectedDate)}` : ''}
              />
            </Grid>

            {/* Time Selection */}
            <Grid item xs={12}>
              <Typography variant="h6" className="mb-3">
                Available Times {availableTimes.length > 0 && `(${availableTimes.length} slots available)`}
              </Typography>
              {loadingTimes ? (
                <p className="text-gray-500">Loading available times...</p>
              ) : availableTimes.length > 0 ? (
                <Grid container spacing={2}>
                  {availableTimes.map((time, index) => (
                    <Grid item key={index}>
                      <Button
                        variant={selectedTime === time ? 'outlined' : 'outlined'}
                        onClick={() => setSelectedTime(time)}
                        style={{
                          backgroundColor: selectedTime === time ? '#ea7200' : 'transparent',
                          borderColor: '#ea7200',
                          color: selectedTime === time ? 'white' : '#ea7200',
                        }}
                      >
                        {formatTimeForDisplay(time)}
                      </Button>
                    </Grid>
                  ))}
                </Grid>
              ) : selectedDate && selectedService && selectedProvider ? (
                <p className="text-gray-500">No available times for the selected date</p>
              ) : (
                <p className="text-gray-500">Please select service, provider, and date to see available times</p>
              )}
            </Grid>

            {/* Additional Information */}
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Additional Information"
                multiline
                rows={4}
                value={additionalInfo}
                onChange={(e) => setAdditionalInfo(e.target.value)}
                placeholder="Enter any additional notes or information for this appointment..."
                disabled={bookingLoading}
              />
            </Grid>

            {/* Action Buttons */}
            <Grid item xs={12}>
              <Box display="flex" gap={2} justifyContent="flex-end">
                <Button
                  variant="outlined"
                  onClick={() => router.back()}
                >
                  Cancel
                </Button>
                <Button
                  variant="contained"
                  disabled={!isFormValid || bookingLoading}
                  style={{
                    backgroundColor: '#ea7200',
                    color: 'white',
                    opacity: (!isFormValid || bookingLoading) ? 0.6 : 1,
                  }}
                  onClick={handleBookAppointment}
                  startIcon={bookingLoading ? <CircularProgress size={20} color="inherit" /> : null}
                >
                  {bookingLoading ? 'Booking...' : 'Book Appointment'}
                </Button>
              </Box>
            </Grid>
          </Grid>
        </Paper>

        {/* Member Selection Modal */}
        <MemberSelectionModal
          open={memberModalOpen}
          onClose={() => setMemberModalOpen(false)}
          onSelect={(member) => setSelectedMember(member)}
          orgId={networkDetails?.id}
        />
      </div>
    </MainLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const { req, query } = context;
    const networkId = query.id as string;
    const serviceId = query.serviceId as string;
    let token;
    let orgId;

    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);
      orgId = cookies.orgID;
      token = cookies.token;
    }

    if (!token || !orgId) {
      return {
        props: {
          networkDetails: null,
          networkOverview: null,
          error: 'Authentication required',
        },
      };
    }

    const [networkDetailsResponse, networkOverviewResponse] = await Promise.all([
      API.NETWORKS.fetchConnectedNetworkDetails(networkId, token, orgId),
      API.NETWORKS.fetchNetworkOverview(networkId, token, orgId),
    ]);

    return {
      props: {
        networkDetails: networkDetailsResponse.data.org,
        networkOverview: networkOverviewResponse.data.org,
        preSelectedServiceId: serviceId || null,
        error: null,
      },
    };
  } catch (error: any) {
    console.error('Error fetching booking data:', error);
    
    return {
      props: {
        networkDetails: null,
        networkOverview: null,
        error: error?.message || 'Failed to load booking information',
      },
    };
  }
}

export default BookAppointment;
