/* eslint-disable react/destructuring-assignment */
/* eslint-disable no-void */
import { Button } from '@mui/material';
import API from 'api/src/lib/api';
import NewEncounter from 'components/members/encounters/new-encounter';
import { useLoading } from 'contexts/loading-context/loading-context';
import { parse } from 'cookie';
import useAnswerLogic from 'hooks/use-answer';
import useAuth from 'hooks/use-auth';
import useIcon from 'hooks/use-icon';
import { GetServerSidePropsContext } from 'next';
import { Questions, Section } from 'types/members-response-type';
import { useEffect, useState } from 'react';
import { capitalizeWords, groupBy } from 'utils/helper';
import { useModal } from 'contexts/modal-context/modal-context';
import MemberLayout from 'layouts/member-details-layout';
import { Check } from '@mui/icons-material';

interface DetailProps {
  sections: Section[];
  title: string;
  sKey: string;
  member: any;
  survey: string;
  orgId: string;
  memberId: string;
}

function Detail(props: DetailProps) {
  const [open, setOpen] = useState(false);
  const [sections, setSections] = useState<any[]>([]);
  const [questions, setQuestions] = useState<Questions[]>([]);
  const [answers, setAnswers] = useState<any[]>([]);
  const { showModal } = useModal();
  const { IconSet, Icon } = useIcon();
  const { isComplete } = useAnswerLogic();
  const { showLoading, hideLoading } = useLoading();
  const { user } = useAuth();
  useEffect(() => {
    if (props?.sections) {
      // eslint-disable-next-line react/destructuring-assignment
      setSections(props.sections);
    }
  }, [props?.sections]);

  const handleSendAnswers = async () => {
    // eslint-disable-next-line max-len
    const allSectionsComplete = sections.every((section) => {
      const groupedQuestions = groupBy(
        section.questions,
        (question) => question.type,
      );
      // eslint-disable-next-line max-len
      return groupedQuestions.every((group: any) => isComplete(answers, group, props.sKey, group[0]?.id));
    });

    if (!allSectionsComplete) {
      showModal('Error', 'Please complete all sections before submitting');
      return;
    }
    showLoading();
    // eslint-disable-next-line no-restricted-syntax
    for await (const answer of answers) {
      await API.MEMBERS.sendSurveyAnswers(answer);
    }
    await API.MEMBERS.closeEncounter(props?.survey);
    hideLoading();
    window.history.back();
  };

  return (
    <MemberLayout
      memberId={props?.memberId}
      active="encounters"
      fullName={`${props?.member?.firstName} ${props?.member?.lastName}`}
      updateLabel="Encounters"
      updateUrl={`/members/encounters/${props?.member?.id}`}
      add
      addLabel={capitalizeWords(props?.sKey, true)}
      rightSide={(
        <Button
          onClick={handleSendAnswers}
          variant="contained"
          className="rounded-md m-0 text-[15px] text-white bg-primary !min-w-[50px]"
        >
          <Check className="mr-1 w-5 h-5" />
          Save
        </Button>
      )}
    >
      <div className="pl-10">
        {sections
          && sections.map((section: Section) => (
            <div key={section.id} className="grid grid-flow-row gap-2">
              <h2 className="font-[500] text-2xl ">
                {capitalizeWords(section.title, true)}
              </h2>
              {groupBy(section.questions, (x) => x.type).map(
                (group: any, index) => (
                  <div className="grid grid-flow-col gap-3 auto-cols-auto items-center pb-2 pt-3 borderLine ">
                    <div className="grid grid-flow-col auto-cols-max gap-3 items-center">
                      <div
                        className={`w-14 h-14 rounded-full grid items-center justify-center ${
                          isComplete(answers, group, props.sKey, group[0].id)
                            ? 'bg-[#E3F8FA]'
                            : 'bg-[#F7F8FA]'
                        }`}
                      >
                        <Icon
                          className={`${
                            isComplete(answers, group, props.sKey, group[0].id)
                              ? 'text-[#008390]'
                              : 'text-[#8996A2]'
                          }`}
                          component={
                            // eslint-disable-next-line no-nested-ternary
                            IconSet[group[0].type]
                              ? IconSet[group[0].type]
                              : isComplete(
                                answers,
                                group,
                                props.sKey,
                                group[0].id,
                              )
                                ? IconSet.check
                                : IconSet[group[0].type]
                          }
                        />
                      </div>
                      <div>
                        <p className="font-[500] p-0 m-0">
                          {props?.sKey === 'full_hrp'
                            ? capitalizeWords(group[0].title, true)
                            : `Section - ${index + 1}`}
                        </p>
                        {/* <p className="font-[400] p-0 m-0">
                        Status:
                        {' '}
                        <span className={`${isComplete(answers, group, props.sKey, group[0].id) ? 'text-green-600' : 'text-[#8996A2]'}`}>
                          {
                          isComplete(answers, group, props.sKey, group[0].id) ? 'Complete' : 'Incomplete'
                        }
                        </span>
                      </p> */}
                      </div>
                    </div>
                    {isComplete(answers, group, props.sKey, group[0].id) ? (
                      <div className="flex justify-end gap-2">
                        <div className="flex items-center gap-2 bg-[#E3F8FA] text-[#008390] px-4 py-2 rounded-lg text-[15px] cursor-default">
                          <Check className="w-5 h-5" />
                          <span>Complete</span>
                        </div>
                        <Button
                          className="grey-btn text-[#E97100] rounded-md gap-2 text-[15px]"
                          onClick={() => {
                            setOpen(true);
                            setQuestions(group);
                          }}
                        >
                          <span>Redo Section</span>
                        </Button>
                      </div>
                    ) : (
                      <div className="flex justify-end">
                        <Button
                          onClick={() => {
                            setOpen(true);
                            setQuestions(group);
                          }}
                          className="grey-btn text-[#E97100] rounded-md gap-2 text-[15px]"
                        >
                          Begin Section
                        </Button>
                      </div>
                    )}
                  </div>
                ),
              )}
            </div>
          ))}
        {open && (
          <NewEncounter
            memberId={props?.memberId}
            surveyId={props?.survey}
            orgId={props?.orgId}
            takerId={user?.id as string}
            sKey={props?.sKey as string}
            open={open}
            setOpen={setOpen}
            questions={questions}
            setQuestions={setQuestions}
            answers={answers}
            setAnswers={setAnswers}
            subTitle={capitalizeWords(props?.title as string, true)}
          />
        )}
      </div>
    </MemberLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const req: any = context;
    if (!req.query?.sections) {
      return {
        redirect: {
          destination: '/members',
          permanent: false,
        },
      };
    }

    // eslint-disable-next-line no-underscore-dangle
    const _sections = JSON.parse(req.query?.sections);
    let token;
    let orgId;
    if (req.req.headers.cookie) {
      const cookies = parse(req.req.headers.cookie);
      orgId = cookies.orgID;
      token = cookies.token; // get cookie from context.req.headers.cookie named token
    }
    
    const memberId = _sections.member;
    const member = await API.MEMBERS.fetchMember(memberId, token, orgId);
    const request: any = await API.MEMBERS.fetchSurveyById(
      _sections.survey,
      token,
      orgId,
    );

    const { sections } = request;
    return {
      props: {
        sections: sections || [],
        title: _sections.title,
        sKey: _sections.sKey,
        survey: _sections.survey,
        orgId,
        member,
        memberId,
      },
    };
  } catch (error) {
    return {
      props: {
        sections: [],
        title: '',
        sKey: '',
        member: [],
        memberId: '',
      },
    };
  }
}

export default Detail;
