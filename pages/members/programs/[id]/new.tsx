/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable max-len */
// import { useRouter } from 'next/router';
import { GetServerSidePropsContext } from 'next';
import { parse } from 'cookie';
import API from 'api/src/lib/api';
import MemberLayout from 'layouts/member-details-layout';
import { Button, TextField, Checkbox } from '@mui/material';
import CheckIcon from '@mui/icons-material/Check';
import SelectBox from 'components/select-box/select-box';
import { useState, useContext, useEffect } from 'react';
import { ConstantsContext } from 'contexts/constant-context/constant-context';
import dayjs from 'dayjs';
import DateSelector from 'components/date-selector/date-selector';
import useAuth from 'hooks/use-auth';
import router from 'next/router';
import EditableTitle from 'components/common/EditableTitle';

interface NewProgramProps {
  member: any;
  team: any;
  programTemplates: any;
}
function NewProgram({ member, team, programTemplates }: NewProgramProps) {
  const [program, setProgram] = useState<any>({});
  const [programTemplate, setProgramTemplate] = useState<any>(null);
  const constant = useContext(ConstantsContext);
  const { user } = useAuth();
  const [updateCounter, setUpdateCounter] = useState(0);
  // const programTemplates = [
  //   {
  //     orgId: 'org_roseman_genesis',
  //     templateId: 'ltss_template_001',
  //     programType: 'LTSS',
  //     displayName: 'Long-Term Services and Supports',
  //     description:
  //       'This program supports individuals with long-term care needs by organizing periodic assessments and program reviews to ensure ongoing eligibility, appropriate interventions, and comprehensive care planning.',
  //     reviewFrequencyDaysDays: 90,
  //     daysToCompleteAssessment: 30,

  //     requiredAssessments: [
  //       {
  //         itemKey: 'adl_iadl',
  //         title: 'ADL/IADL Functional Assessment',
  //         required: true,
  //       },
  //       {
  //         itemKey: 'sdoh',
  //         title: 'Social Determinants of Health (SDOH)',
  //         required: true,
  //       },
  //       {
  //         itemKey: 'behavioral_screening',
  //         title: 'Behavioral Health Screening',
  //         required: false,
  //       },
  //     ],

  //     // programReviews: [
  //     //   {
  //     //     itemKey: 'program_review',
  //     //     title: 'Quarterly Program Review',
  //     //     required: true,
  //     //   },
  //     //   {
  //     //     itemKey: 'barriers_review',
  //     //     title: 'Barriers and Supports Review',
  //     //     required: false,
  //     //   },
  //     // ],
  //   },
  // ];
  const createAssessmentObject = (assessment: any) => ({
    itemKey: assessment.itemKey,
    taskSetting: '',
    daysToCompleteAssessment: null,
    assignee: null,
  });

  useEffect(() => {
    setProgram((prev: any) => ({
      ...prev,
      assignedTo: member?.primaryUser
        ? `${member?.primaryUser?.firstName} ${member?.primaryUser?.lastName}`
        : '',
      assignedToId: member?.primaryUser?.id || '',
    }));
  }, []);

  useEffect(() => {
    if (program.programKey) {
      setProgramTemplate(
        programTemplates.find(
          (template: any) => template.programKey.toLowerCase()
            === program.programKey.toLowerCase(),
        ),
      );
    }
  }, [program.programKey]);

  useEffect(() => {
    if (programTemplate) {
      setProgram((prev: any) => ({
        ...prev,
        programType: programTemplate.programKey,
        displayName: programTemplate.programName,
        requiredAssessments: [],
        reviewFrequencyDays:
          programTemplate.programConfig.reviewFrequencyDays || 90,
        status: 'active',
        assignedBy: `${user?.firstName} ${user?.lastName}`,
      }));
    }
  }, [programTemplate]);

  useEffect(() => {
    console.log(program);
  }, [program]);

  useEffect(() => {
    if (program.startDate && program.endDate && program.reviewFrequencyDays) {
      const start = dayjs(program.startDate);
      const end = dayjs(program.endDate);

      // total number of days (inclusive)
      const totalDays = end.diff(start, 'day') + 1;
      const numPeriods = Math.ceil(totalDays / program.reviewFrequencyDays);

      const periods: any[] = [];

      let currentStart = start;

      for (let i = 0; i < numPeriods; i += 1) {
        const periodNumber = i + 1;
        const periodStart = currentStart;
        let periodEnd = periodStart.add(program.reviewFrequencyDays, 'day');

        if (periodStart.isAfter(end)) {
          break;
        }

        if (periodEnd.isAfter(end)) {
          periodEnd = end;
        }

        periods.push({
          periodNumber,
          startDate: periodStart.startOf('day').toISOString(),
          endDate: periodEnd.startOf('day').toISOString(),
          status: i === 0 ? 'active' : 'pending',
          outcomeStatus: null,
          outcomeDescription: null,
          tasks: [],
          title: `Program Review ${periodNumber}`,
        });

        currentStart = periodEnd.add(1, 'day');
      }

      setProgram((prev: any) => ({
        ...prev,
        reviewPeriods: periods,
      }));
    }
  }, [program.startDate, program.endDate, program.reviewFrequencyDays]);

  useEffect(() => {
    if (!program.requiredAssessments || !program.reviewPeriods) {
      return;
    }
    setProgram((prev: any) => {
      const updatedReviewPeriods = prev.reviewPeriods.map((period: any) => {
        // Map each required assessment into tasks
        const updatedTasks = prev.requiredAssessments.map((assessment: any) => {
          // Check if task already exists
          // const existingTask = period.tasks?.find(
          //   (t: any) => t.assessmentKey === assessment.itemKey,
          // );
          // if (existingTask) return existingTask;

          // Use assessment.daysToCompleteAssessment for due date if provided
          const daysToComplete = assessment.daysToCompleteAssessment
            || programTemplate?.programConfig?.daysToCompleteAssessment;
          return {
            taskType: 'assessment',
            title: programTemplate?.programConfig?.requiredAssessments?.find(
              (a: any) => a.itemKey === assessment.itemKey,
            )?.title,
            assessmentKey: assessment.itemKey,
            reviewKey: null,
            assignedTo: assessment.assignee
              ? `${
                team?.navigators?.find(
                  (n: any) => n.id?.toLowerCase() === assessment.assignee?.toLowerCase(),
                )?.firstName
              } ${
                team?.navigators?.find(
                  (n: any) => n.id?.toLowerCase() === assessment.assignee?.toLowerCase(),
                )?.lastName
              }`
              : '',
            completedAt: null,
            dueDate: dayjs(period.startDate)
              .add(daysToComplete, 'day')
              .startOf('day')
              .toISOString(),
            status: 'pending',
          };
        });

        return {
          ...period,
          // Only update tasks; leave other fields intact
          tasks: updatedTasks,
        };
      });

      return {
        ...prev,
        reviewPeriods: updatedReviewPeriods,
      };
    });
  }, [program.requiredAssessments, updateCounter]); // <--- only depends on requiredAssessments

  const handleSaveTitle = (periodNumber: number, newTitle: string) => {
    setProgram((prev: any) => ({
      ...prev,
      reviewPeriods: prev.reviewPeriods.map((period: any) => (period.periodNumber === periodNumber
        ? { ...period, title: newTitle }
        : period)),
    }));
  };

  const handleCancel = () => {
    router.push(`/members/programs/${member.id}`);
  };

  const handleSave = () => {
    const updatedReviewPeriods = program.reviewPeriods.map((period: any) => {
      // assessment tasks
      const assessmentTasks = (program.requiredAssessments || []).map(
        (assessment: any) => {
          const daysToComplete = assessment.daysToCompleteAssessment
            || programTemplate?.programConfig?.daysToCompleteAssessment;

          return {
            taskType: 'assessment',
            title: programTemplate?.programConfig?.requiredAssessments?.find(
              (a: any) => a.itemKey === assessment.itemKey,
            )?.title,
            reviewKey: null,
            assignedTo: assessment.assignee
              ? `${
                team?.navigators?.find(
                  (n: any) => n.id?.toLowerCase() === assessment.assignee?.toLowerCase(),
                )?.firstName
              } ${
                team?.navigators?.find(
                  (n: any) => n.id?.toLowerCase() === assessment.assignee?.toLowerCase(),
                )?.lastName
              }`
              : '',
            assignedToId: assessment.assignee,
            completedAt: null,
            dueDate: dayjs(period.startDate)
              .add(daysToComplete, 'day')
              .startOf('day')
              .toISOString(),
            status: 'pending',
          };
        },
      );

      // review_period task
      const reviewPeriodTask = {
        taskType: 'review_period',
        title: `Review Period ${period.periodNumber}`,
        assessmentKey: `review_period_${period.periodNumber}`,
        reviewKey: null,
        assignedToId: period.assignee || '',
        assignedTo: period.assignee
          ? `${
            team?.navigators?.find(
              (n: any) => n.id?.toLowerCase() === period.assignee?.toLowerCase(),
            )?.firstName
          } ${
            team?.navigators?.find(
              (n: any) => n.id?.toLowerCase() === period.assignee?.toLowerCase(),
            )?.lastName
          }`
          : '',
        completedAt: null,
        dueDate: dayjs(period.endDate).startOf('day').toISOString(),
        status: 'pending',
      };

      return {
        ...period,
        tasks: [...assessmentTasks, reviewPeriodTask],
      };
    });

    const finalProgram = {
      ...program,
      reviewPeriods: updatedReviewPeriods,
    };

    console.log('Saving Program:', finalProgram);
    // 🔥 call API here, e.g.
    // API.post('/programs', finalProgram)
  };

  return (
    <MemberLayout
      memberId={member.id}
      active="programs"
      fullName={`${member?.firstName} ${member?.lastName}`}
      updateLabel="Programs"
      updateUrl={`/members/programs/${member.id}`}
      addLabel="New Program"
      add
      rightSide={(
        <div className="flex flex-row gap-2 items-center">
          <Button
            onClick={handleCancel}
            className="rounded-md m-0 text-[15px] text-[#262D2D] grey-btn"
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={handleSave}
            color="success"
            className="rounded-md !min-w-[50px]"
          >
            <CheckIcon
              className="mr-[6px]"
              style={{ width: '20px', height: '20px' }}
            />
            Save
          </Button>
        </div>
      )}
    >
      <div className="w-full pl-10 pb-10">
        <p className="m-0 p-0 font-[600] text-lg mb-4">Program Details</p>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-[30px] gap-y-4">
          <SelectBox
            key="programType"
            label="Program Type"
            keyVal="programType"
            defaultValue={programTemplate?.programKey || ''}
            onChange={(e) => {
              const selectedValue = e.target.value;
              setProgramTemplate(
                programTemplates.find(
                  (it: any) => it.programKey === selectedValue,
                ),
              );
            }}
            items={
              programTemplates?.map((template: any) => ({
                key: template.programKey,
                title: template.programName,
              })) || []
            }
          />
          <DateSelector
            key="startDate"
            name="startDate"
            defaultValue={program?.startDate}
            pickerProps={{
              label: 'Start Date',
              slotProps: {
                field: {
                  clearable: true,
                  onClear: () => setProgram({
                    ...program,
                    startDate: '',
                  }),
                },
              },
            }}
            onChange={(e) => {
              const date = dayjs(e.target.value).format('YYYY-MM-DD');
              setProgram({
                ...program,
                startDate: date,
              });
            }}
          />
          <DateSelector
            key="endDate"
            name="endDate"
            defaultValue={program?.endDate}
            pickerProps={{
              label: 'End Date',
              slotProps: {
                field: {
                  clearable: true,
                  onClear: () => setProgram({
                    ...program,
                    endDate: '',
                  }),
                },
              },
            }}
            onChange={(e) => {
              const date = dayjs(e.target.value).format('YYYY-MM-DD');
              setProgram({
                ...program,
                endDate: date,
              });
            }}
          />
          <SelectBox
            key="reviewFrequencyDays"
            label="Review Frequency"
            keyVal="reviewFrequencyDays"
            defaultValue={program?.reviewFrequencyDays || ''}
            onChange={(e) => {
              setProgram({
                ...program,
                reviewFrequencyDays: parseInt(e.target.value, 10),
              });
            }}
            items={constant?.reviewFrequencies || []}
          />
          <SelectBox
            key="assignedTo"
            label="Assigned to"
            keyVal="assignedTo"
            defaultValue={program?.assignedToId?.toLowerCase() || ''}
            onChange={(e) => {
              const selectedNavigator = team?.navigators?.find(
                (n: any) => n.id?.toLowerCase() === e.target.value?.toLowerCase(),
              );
              setProgram({
                ...program,
                assignedTo: `${selectedNavigator?.firstName} ${selectedNavigator?.lastName}`,
                assignedToId: e.target.value || '',
              });
            }}
            items={
              team?.navigators?.map((navigator: any) => ({
                key: navigator.id,
                title: `${navigator.firstName} ${navigator.lastName}`,
              })) || []
            }
          />
          {/* <TextField
            key="assignedTo"
            name="assignedTo"
            label="Assigned to"
            value={program?.assignedTo || ''}
            variant="filled"
            onChange={(e) => setProgram({
              ...program,
              assignedTo: e.target.value,
            })}
            fullWidth
          /> */}
        </div>
        <div>
          <TextField
            key="description"
            name="description"
            label="Enter program description (optional)..."
            value={program?.description}
            variant="filled"
            multiline
            rows={6}
            onChange={(e) => setProgram({
              ...program,
              description: e.target.value,
            })}
            fullWidth
            sx={{ backgroundColor: '#F7F8FA', marginTop: 2 }}
          />
        </div>

        <p className="m-0 p-0 font-[600] text-lg mt-[50px] mb-4">
          Required Assessments
        </p>
        {!programTemplate && (
          <p className="m-0 p-0 font-[400] text-sm italic mb-4 text-gray-600">
            Select program type to view required assessments.
          </p>
        )}
        {programTemplate
          && programTemplate?.programConfig?.requiredAssessments?.length > 0 && (
            <>
              <div className="flex items-center mb-1 ml-[-10px]">
                <Checkbox
                  checked={
                    program.requiredAssessments?.length
                    === programTemplate?.programConfig?.requiredAssessments?.length
                  }
                  onChange={() => {
                    const allSelected = program.requiredAssessments?.length
                      === programTemplate?.programConfig?.requiredAssessments
                        ?.length;

                    setProgram((prev: any) => ({
                      ...prev,
                      requiredAssessments: allSelected
                        ? [] // unselect all
                        : programTemplate?.programConfig?.requiredAssessments?.map(
                          createAssessmentObject,
                        ),
                    }));
                  }}
                />
                <p
                  className={`m-0 p-0 text-sm ${
                    program.requiredAssessments?.length
                    === programTemplate?.programConfig?.requiredAssessments?.length
                      ? 'font-[500]'
                      : 'font-[400]'
                  }`}
                >
                  Select All
                </p>
              </div>

              {programTemplate?.programConfig?.requiredAssessments?.map(
                (assessment: any) => {
                  const selectedAssessment = (
                    program.requiredAssessments || []
                  ).find((a: any) => a.itemKey === assessment.itemKey);
                  const isChecked = !!selectedAssessment;

                  return (
                    <div key={assessment.itemKey} className="mb-1 ml-[-10px]">
                      <div className="flex items-center mb-1">
                        <Checkbox
                          checked={isChecked}
                          onChange={() => {
                            setProgram((prev: any) => {
                              const exists = (
                                prev.requiredAssessments || []
                              ).some(
                                (a: any) => a.itemKey === assessment.itemKey,
                              );

                              return {
                                ...prev,
                                requiredAssessments: exists
                                  ? prev.requiredAssessments.filter(
                                    (a: any) => a.itemKey !== assessment.itemKey,
                                  )
                                  : [
                                    ...(prev.requiredAssessments || []),
                                    createAssessmentObject(assessment),
                                  ],
                              };
                            });
                          }}
                        />
                        <p
                          className={`m-0 p-0 text-sm ${
                            isChecked ? 'font-[500]' : 'font-[400]'
                          }`}
                        >
                          {assessment.title}
                        </p>
                      </div>

                      {/* CONDITIONAL ROW OF SELECT BOXES */}
                      {isChecked && (
                        <div className="grid grid-cols-1 sm:grid-cols-3 gap-x-4 gap-y-2 ml-3 pb-4 border-bottom">
                          {/* Days to complete */}
                          <SelectBox
                            key={`${assessment.itemKey}-daysToCompleteAssessment`}
                            label="Days to Complete Assessment"
                            keyVal="daysToCompleteAssessment"
                            defaultValue={
                              selectedAssessment?.daysToCompleteAssessment
                              || programTemplate?.programConfig
                                ?.daysToCompleteAssessment
                              || ''
                            }
                            onChange={(e) => {
                              const newValue = parseInt(e.target.value, 10);
                              setProgram((prev: any) => ({
                                ...prev,
                                requiredAssessments: (
                                  prev.requiredAssessments || []
                                ).map((a: any) => (a.itemKey === assessment.itemKey
                                  ? {
                                    ...a,
                                    daysToCompleteAssessment: newValue,
                                  }
                                  : a)),
                              }));
                              setUpdateCounter((c) => c + 1); // forces useEffect if needed
                            }}
                            items={constant?.assessmentDueOptions || []}
                          />

                          {/* Task setting */}
                          <SelectBox
                            key={`${assessment.itemKey}-taskSetting`}
                            label="Task Setting"
                            keyVal="taskSetting"
                            defaultValue={selectedAssessment?.taskSetting || ''}
                            onChange={(e) => {
                              const newValue = e.target.value;
                              setProgram((prev: any) => ({
                                ...prev,
                                requiredAssessments: (
                                  prev.requiredAssessments || []
                                ).map((a: any) => (a.itemKey === assessment.itemKey
                                  ? {
                                    ...a,
                                    taskSetting: newValue,
                                    assignee:
                                          newValue === 'dontassigntask'
                                            ? null
                                            : a.assignee,
                                  }
                                  : a)),
                              }));
                            }}
                            items={[
                              { title: 'Assign Task', key: 'assigntask' },
                              {
                                title: "Don't Assign Task",
                                key: 'dontassigntask',
                              },
                            ]}
                          />

                          {/* Assignee (only if not "don't assign") */}
                          {selectedAssessment?.taskSetting
                            !== 'dontassigntask' && (
                            <SelectBox
                              key={`${assessment.itemKey}-assignee`}
                              label="Assign to"
                              keyVal="assignee"
                              defaultValue={selectedAssessment?.assignee || ''}
                              onChange={(e) => {
                                const newValue = e.target.value;
                                setProgram((prev: any) => ({
                                  ...prev,
                                  requiredAssessments: (
                                    prev.requiredAssessments || []
                                  ).map((a: any) => (a.itemKey === assessment.itemKey
                                    ? { ...a, assignee: newValue }
                                    : a)),
                                }));
                              }}
                              items={
                                team?.navigators?.map((navigator: any) => ({
                                  key: navigator.id,
                                  title: `${navigator.firstName} ${navigator.lastName}`,
                                })) || []
                              }
                            />
                          )}
                        </div>
                      )}
                    </div>
                  );
                },
              )}
            </>
        )}
        <div className="flex flex-row justify-between items-center mb-4 mt-[50px]">
          <p className="m-0 p-0 font-[600] text-lg">Program Reviews</p>
          {program?.reviewPeriods?.length > 0 && (
            <p className="m-0 p-0 font-[400] text-[15px] italic text-[#008390]">
              {program?.reviewPeriods?.length}
              {' '}
              reviews required based off of
              review frequency above.
            </p>
          )}
        </div>
        {!program?.reviewPeriods && (
          <p className="m-0 p-0 font-[400] text-sm italic mb-4 text-gray-600">
            Select dates and review frequency to view program reviews.
          </p>
        )}
        {program?.reviewPeriods
          && program?.reviewPeriods?.length > 0
          && program?.reviewPeriods?.map((review: any, index: number) => (
            <div key={review.periodNumber} className="mt-8">
              {/* <p className="m-0 p-0 font-[500] text-[15px] pb-4">
                Program Review
                {' '}
                {review.periodNumber}
              </p> */}
              <EditableTitle
                initialTitle={`Program Review ${review.periodNumber}`}
                onSave={(newTitle) => handleSaveTitle(review.periodNumber, newTitle)}
                size={15}
                fontWeight={500}
                fontStyle="'Inter', sans-serif"
                editIconClassName="!ml-2"
              />
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-x-4 gap-y-2 ml-[2px] mt-3">
                <DateSelector
                  key="endDate"
                  name="endDate"
                  defaultValue={review.endDate}
                  pickerProps={{
                    label: 'Complete By',
                    slotProps: {
                      field: {
                        clearable: true,
                        onClear: () => setProgram({
                          ...program,
                          endDate: '',
                        }),
                      },
                    },
                  }}
                  onChange={(e) => {
                    const date = dayjs(e.target.value).format('YYYY-MM-DD');
                    setProgram({
                      ...program,
                      [review.itemKey]: {
                        ...program[review.itemKey],
                        endDate: date,
                      },
                    });
                  }}
                />
                <SelectBox
                  key={`${review.itemKey}-taskSetting`}
                  label="Task Setting"
                  keyVal="taskSetting"
                  defaultValue={program.reviewPeriods[index]?.taskSetting || ''}
                  onChange={(e) => {
                    setProgram((prev: any) => ({
                      ...prev,
                      reviewPeriods: prev.reviewPeriods.map(
                        (period: any, i: number) => (i === index
                          ? {
                            ...period,
                            taskSetting: e.target.value,
                            assignee:
                                  e.target.value === 'dontassigntask'
                                    ? null
                                    : period.assignee,
                          }
                          : period),
                      ),
                    }));
                  }}
                  items={[
                    { title: 'Assign Task', key: 'assigntask' },
                    { title: "Don't Assign Task", key: 'dontassigntask' },
                  ]}
                />
                {program.reviewPeriods[index]?.taskSetting
                  !== 'dontassigntask' && (
                  <div className="relative">
                    <SelectBox
                      key={`${review.itemKey}-assignee`}
                      label={
                        program.reviewPeriods[index]?.taskSetting
                        === 'dontassigntask'
                          ? ''
                          : 'Assign to'
                      }
                      keyVal="assignee"
                      defaultValue={
                        program.reviewPeriods[index]?.assignee || ''
                      }
                      onChange={(e) => {
                        setProgram((prev: any) => ({
                          ...prev,
                          reviewPeriods: prev.reviewPeriods.map(
                            (period: any, i: number) => (i === index
                              ? {
                                ...period,
                                assignee: e.target.value,
                              }
                              : period),
                          ),
                        }));
                      }}
                      items={
                        team?.navigators?.map((navigator: any) => ({
                          key: navigator.id,
                          title: `${navigator.firstName} ${navigator.lastName}`,
                        })) || []
                      }
                      disabled={
                        program.reviewPeriods[index]?.taskSetting
                        === 'dontassigntask'
                      }
                    />
                  </div>
                )}
              </div>
            </div>
          ))}
      </div>
    </MemberLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const { req, query } = context;
    let token;
    let org;
    let teamId;

    const memberId: string = query.id as string;

    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);
      token = cookies.token;
      org = cookies.orgID;
      teamId = cookies.teamID;
    }

    const member = await API.MEMBERS.fetchMember(memberId, token, org);
    const team = await API.TEAMS.fetchSingleTeam(teamId as string, token, org);
    const programTemplates = await API.MEMBERS.fetchOrgPrograms(
      org as string,
      token,
    );

    return {
      props: {
        member: member || [],
        team: team || [],
        programTemplates: programTemplates || [],
      },
    };
  } catch (error) {
    return {
      props: {
        member: [],
        team: [],
        programTemplates: [],
      },
    };
  }
}

export default NewProgram;
