import { GetServerSidePropsContext } from 'next';
import { parse } from 'cookie';
import API from 'api/src/lib/api';
import MemberLayout from 'layouts/member-details-layout';
import { Button } from '@mui/material';
import React, { useContext, useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { ConstantsContext } from 'contexts/constant-context/constant-context';
import CustomTable from 'components/table-components/table';

interface ProgramsProps {
  member: any;
  programs: any[];
}
function Programs({ member, programs }: ProgramsProps) {
  const router = useRouter();
  const constants = useContext(ConstantsContext);

  useEffect(() => {
    console.log(programs);
  }, [programs]);

  return (
    <MemberLayout
      memberId={member.id}
      active="programs"
      fullName={`${member?.firstName} ${member?.lastName}`}
      updateLabel="Programs"
    >
      <div className="w-full pt-1 pb-1 justify-between pl-10 grid grid-flow-col auto-cols-max items-center">
        <p className="m-0 p-0 font-[600] text-lg">
          Programs (
          {programs?.length}
          )
        </p>
        <Button
          onClick={() => router.push(`/members/programs/${member.id}/new`)}
          variant="contained"
          color="success"
          component="label"
          className="rounded-md m-0 p-0 h-[40px] float-right"
        >
          + Add Program
        </Button>
      </div>

      <div className="w-full pl-10">
        {programs?.length > 0 ? (
          <CustomTable
            variant="member-programs-table"
            source={programs}
            style={{
              border: 'solid 1px lightgray',
              borderRadius: '10px',
              borderBottom: 'none',
              marginTop: '16px',
            }}
            headCellStyle={{
              padding: '4px 16px',
              fontSize: '13px',
              color: '#747A7A',
            }}
            tableCellStyle={{ padding: '0px 16px', fontSize: '15px' }}
          />
        ) : (
          <p className="text-[15px] font-[400] m-0 mt-1">
            No programs have been listed. Add the first one.
          </p>
        )}
      </div>
    </MemberLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const { req } = context;
    let token;
    let org;
    // let orgId;
    const memberId: string = context.query.id as string;
    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);
      //   orgId = cookies.orgID;
      token = cookies.token; // get cookie from context.req.headers.cookie named token
      org = cookies.orgID;
    }
    const member = await API.MEMBERS.fetchMember(memberId, token, org);
    // eslint-disable-next-line max-len
    const programs: any = await API.MEMBERS.fetchMemberPrograms(memberId, token);
    return {
      props: {
        programs: programs || [],
        member: member || [],
      },
    };
  } catch (error) {
    return {
      props: {
        programs: [],
        member: [],
      },
    };
  }
}

export default Programs;
