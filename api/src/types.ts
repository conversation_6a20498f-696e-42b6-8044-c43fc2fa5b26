export interface UserSignUp {
    username: string;
    password: string;
    firstName: string;
    lastName: string;
    roles: string[];
    orgID: string;
}

export interface UserLogin {
    username: string;
    password: string;
}

export interface PasswordReset {
    email: string;
    password: string;
}

export interface UserUpdate {
    firstName?: string;
    lastName?: string;
    roles?: string[];
    phone?: string;
    meta?: any;
    profile?: string;
    color?: string;
    teams?: string[];
}

export interface UserAttachment {
    type: string;
    name: string;
    kind:string;
    base64: string;
}

export interface MemberAttachment {
    type: string;
    name: string;
    base64: string;
}

export interface MemberCreate {
    orgID: string;
    email: string;
    firstName: string;
    middleName: string;
    lastName: string;
    type: string;
    roles: string[];
    dob: string;
    gender: string;
    ethnicity: string;
    sexualIdentity: string;
    genderIdentity: string;
    pronouns: string;
    lang: string;
    referredBy: string;
    status: string;
}

export interface StudentCreate {
    orgID: string;
    email: string;
    firstName: string;
    middleName: string;
    lastName: string;
    type: string;
    roles: string[];
    dob: string;
    gender: string;
    ethnicity: string;
    sexualIdentity: string;
    genderIdentity: string;
    pronouns: string;
    lang: string;
    referredBy: string;
    status: string;
    school: {
        name: string;
        grade: string;
    };
}

export interface MemberAddress {
    street: string;
    street2: string;
    city: string;
    state: string;
    zip: string;
    kind: string;
}

// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface FetchHouseholdsRequest {}

export interface FetchSingleHouseholdRequest {
  householdId: string;
}

export interface CreateHouseholdRequestBody {
  orgID: string;
  title: string;
  type: string;
  kind: string;
  headOfHouse: object;
}

export interface UpdateHouseHoldRequestBody {
  householdId: string;
  body:{
    title?:string;
    address?: MemberAddress;
    members?: string[];
    teams?: string[];
  }
}
export interface CreateHouseholdRequest {
  body: CreateHouseholdRequestBody;
}
export interface AddMemberToHouseholdRequest{
  householdId: string;
  body: {
    members: string[];
  }
}
export interface AddTeamToHouseholdRequest{
  householdId: string;
  body: {
    teams: string[];
  }
}
export interface CreateHouseholdAttachmentRequestBody {
  type: string;
  name: string;
  base64: string;
}

export interface CreateHouseholdAttachmentRequest {
  householdId: string;
  body: CreateHouseholdAttachmentRequestBody;
}

export interface RemoveTeamFromHouseholdRequestBody {
  teams: string[];
}

export interface RemoveTeamFromHouseholdRequest {
  householdId: string;
  body: RemoveTeamFromHouseholdRequestBody;
}

export interface RemoveHeadOfHouseRequest {
  householdId: string;
}

export interface RemoveMemberFromHouseholdRequest {
  householdId: string;
  memberId: string;
}

export interface AddUsersToTeamsRequestBody {
    navigators: string[];
  }

export interface AddUsersToTeamsRequest {
    teamId: string;
    body: AddUsersToTeamsRequestBody;
  }

// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface FetchTeamsRequest {}

export interface FetchTeamHouseholdsRequest {
    teamId: string;
  }

export interface CreateTeamRequestBody {
    orgID: string;
    name: string;
    address: {
      street?: string;
      city?: string;
      state?: string;
      zip?: string;
      kind: string;
    };
    navigators: string[];
  }

export interface CreateTeamRequest {
    body: CreateTeamRequestBody;
  }

export interface CreateTeamLeadsRequestBody {
    leads: string[];
  }

export interface CreateTeamLeadsRequest {
    teamId: string;
    body: CreateTeamLeadsRequestBody;
  }

export interface RemoveTeamRequest {
    teamId: string;
  }

export interface Network {
  phones: any[];
  id: string;
  updatedAt: string;
  status: string;
  contact?: string;
  types: string[];
  address: any[];
  createdAt: string;
  services: Service[];
  name: string;
  org: Org;
  website?: any;
}

export interface ConnectedNetwork {
  id: string;
  name: string;
  desc?: string;
  addresses: Array<{ address: string }>;
  phones: Array<{ number: string }>;
}

export interface ConnectedNetworksResponse {
  data: {
    org: {
      children: ConnectedNetwork[];
    };
  };
}

export interface ConnectedNetworkService {
  id: string;
  title: string;
  description?: string;
  duration: number;
  kind: 'queue' | 'appointment';
  nextAvailableAppt?: Array<{
    startIso: string;
  }>;
}

export interface ConnectedNetworkDetails {
  id: string;
  name: string;
  desc?: string;
  addresses: Array<{ address: string }>;
  phones: Array<{ number: string }>;
  services: ConnectedNetworkService[];
}

export interface ConnectedNetworkDetailsResponse {
  data: {
    org: ConnectedNetworkDetails;
  };
}

export interface NetworkProvider {
  id: string;
  firstName: string;
  lastName: string;
}

export interface NetworkService {
  id: string;
  title: string;
  kind: 'queue' | 'appointment';
  durationMins: number;
}

export interface NetworkOverviewResponse {
  data: {
    org: {
      providers: {
        nodes: NetworkProvider[];
      };
      services: NetworkService[];
    };
  };
}

export interface AvailableTimesRequest {
  org: string;
  service: string;
  date: string;
}

export interface AvailableTimesResponse {
  data: {
    org: {
      service: {
        id: string;
        title: string;
        availableTimesIso: string[];
      };
    };
  };
}

export interface MemberSearchItem {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  attachments?: Array<{
    id: string;
    url: string;
    kind: string;
  }>;
  org: {
    id: string;
  };
}

export interface MemberSearchResponse {
  items: MemberSearchItem[];
  metadata: {
    page: number;
    total: number;
    per: number;
  };
}

export interface AppointmentBookingRequest {
  person: string;
  provider: string;
  kind: string;
  org: string;
  service: string;
  startEpoch: number;
  endEpoch: number;
  reason?: string;
}

export interface AppointmentBookingResponse {
  data: {
    apptBook: {
      errors: string[];
      success: boolean;
      appointment: {
        id: string;
      };
    };
  };
}

export interface NetworkInviteRequest {
  contact: string;
  email: string;
  from: string;
}

export interface NetworkInviteResponse {
  status: number;
  message?: string;
}

interface Service {
  updatedAt: string;
  status: string;
  desc: string;
  id: string;
  type: string;
  name: string;
  createdAt: string;
  service: string;
  org: Org;
}

export interface CreateNetworkRequestBody {
  orgID: string;
  name: string;
  types: string[];
  status: string;
  contact: string;
  website?: string;
  carriers?: string[];
}
interface Address{
    street: string;
    street2: string;
    city: string;
    state: string;
    zip: string;
    kind: string;
}
export interface CreateAddressBody {
  address:Address;
  phone: {
    number: string;
    label: string
   }
}
export interface CreateServiceBody {
  orgID: string;
  name:string;
  desc:string;
  service:string;
}
interface Org {
  id: string;
}
