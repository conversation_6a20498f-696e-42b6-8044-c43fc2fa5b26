# Household ID Integration Update

## Overview
Updated the background check API call to use `householdId` instead of `memberId` and implemented proper prop passing from the parent component.

## Changes Made

### 1. **Updated SecurityCheckModalProps Interface**

**Before:**
```typescript
interface SecurityCheckModalProps {
  open: boolean;
  onClose: () => void;
  address: string;
  onSelectFinding: (finding: SecurityFinding) => void;
}
```

**After:**
```typescript
interface SecurityCheckModalProps {
  open: boolean;
  onClose: () => void;
  address: string;
  onSelectFinding: (finding: SecurityFinding) => void;
  householdId: string; // household ID for API requests
}
```

### 2. **Updated offenderSearchByLocation Function**

**Function Signature:**
```typescript
// Before
async function offenderSearchByLocation(lat: number, lon: number, _normalizedAddr?: string, originalAddr?: string): Promise<SecurityFinding[]>

// After  
async function offenderSearchByLocation(lat: number, lon: number, _normalizedAddr?: string, originalAddr?: string, householdId?: string): Promise<SecurityFinding[]>
```

**Added Validation:**
```typescript
if (!householdId) {
  console.warn('Household ID is required for background check.');
  return [];
}
```

**Updated API Request Body:**
```typescript
// Before
body: JSON.stringify({
  address: originalAddr || '',
  latitude: lat,
  longitude: lon,
  memberId: 'fe3aa001-8465-425b-b6ec-a2de78121db1', // Hardcoded
}),

// After
body: JSON.stringify({
  address: originalAddr || '',
  latitude: lat,
  longitude: lon,
  householdId: householdId,
}),
```

### 3. **Updated SecurityCheckModal Component**

**Component Props:**
```typescript
// Before
export default function SecurityCheckModal({ open, onClose, address, onSelectFinding }: SecurityCheckModalProps)

// After
export default function SecurityCheckModal({ open, onClose, address, onSelectFinding, householdId }: SecurityCheckModalProps)
```

**Function Call:**
```typescript
// Before
const findings = await offenderSearchByLocation(coords.lat, coords.lon, normalizeAddress(address), address);

// After
const findings = await offenderSearchByLocation(coords.lat, coords.lon, normalizeAddress(address), address, householdId);
```

### 4. **Updated Parent Component Usage**

**In `components/households/add-update/index.tsx`:**
```typescript
// Before
<SecurityCheckModal
  open={openSecurity}
  onClose={() => setOpenSecurity(false)}
  address={addressString}
  onSelectFinding={handleSecuritySelect}
/>

// After
<SecurityCheckModal
  open={openSecurity}
  onClose={() => setOpenSecurity(false)}
  address={addressString}
  onSelectFinding={handleSecuritySelect}
  householdId={household.id}
/>
```

## Implementation Approach

### **Option B: Prop Passing (Selected)**
- **Rationale**: The parent component `HouseholdAddUpdate` already has access to `household.id`
- **Benefits**: 
  - Clean and explicit
  - Follows existing patterns in the codebase
  - Type-safe with TypeScript
  - Easy to test and maintain

### **Option A: URL Parameter Extraction (Not Used)**
- **Rationale**: Would require additional URL parsing logic
- **Drawbacks**: 
  - More complex implementation
  - Dependent on URL structure
  - Less reliable than direct prop passing

## Validation and Error Handling

### **Household ID Validation**
```typescript
if (!householdId) {
  console.warn('Household ID is required for background check.');
  return [];
}
```

### **Existing Validations Maintained**
- User authentication check
- API response validation
- Geocoding error handling
- Network request error handling

## API Request Format

### **New Request Structure**
```json
POST /api/background-checks/location-search
Headers:
  Authorization: Bearer {USER_TOKEN}
  Content-Type: application/json
Body:
{
  "address": "343 MAJORCA AVENUE CORAL GABLES, FL 33134",
  "latitude": 25.7553134,
  "longitude": -80.2619766,
  "householdId": "actual-household-uuid"
}
```

### **Key Changes**
- **Field Name**: `memberId` → `householdId`
- **Value Source**: Hardcoded UUID → Dynamic from household prop
- **Validation**: Added null/undefined check

## Benefits

### 1. **Dynamic Data**
- No more hardcoded household IDs
- Each household gets its own background check results
- Proper data isolation between households

### 2. **Type Safety**
- TypeScript interface ensures householdId is provided
- Compile-time validation of prop structure
- Better IDE support and autocomplete

### 3. **Maintainability**
- Clear data flow from parent to child component
- Easy to trace where household ID comes from
- Consistent with other prop-based data passing

### 4. **Reliability**
- Validation ensures API calls only happen with valid household ID
- Graceful degradation when household ID is missing
- Clear error messages for debugging

## Testing Considerations

### **Test Scenarios**
1. **Valid Household ID**: API call should include correct householdId
2. **Missing Household ID**: Should return empty results with warning
3. **Empty Household ID**: Should return empty results with warning
4. **Parent Component**: Should pass household.id correctly

### **Error Cases**
- User not authenticated (existing)
- Household ID missing (new)
- API request failures (existing)
- Geocoding failures (existing)

## Deployment Impact

### ✅ **No Breaking Changes**
- Existing functionality preserved
- Same user interface and experience
- Backward compatible error handling

### ✅ **Improved Data Accuracy**
- Background checks now tied to specific households
- No cross-contamination between household searches
- Better audit trail for security checks

### ✅ **Enhanced Security**
- Proper data isolation
- User-level authentication maintained
- Household-specific access control

The implementation successfully replaces the hardcoded `memberId` with dynamic `householdId` values, providing better data accuracy and proper household-specific background checks.
