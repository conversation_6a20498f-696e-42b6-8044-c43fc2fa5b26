export interface RiskScoreReference {
  type: 'assessment' | 'attachment' | 'careplan' | 'note';
  ref_id: string;
  title: string;
  reason: string;
}

export interface RiskScoreCreate {
  score: number;
  level: 'Low' | 'Medium' | 'High';
  source: 'Manual' | 'AssessmentEngine' | 'Other';
  notes?: string;
  references?: RiskScoreReference[];
}

export interface RiskScore extends RiskScoreCreate {
  id: string;
  createdAt: string;
  updatedAt: string;
  memberId: string;
}

export interface RiskScoreHistory {
  id: string;
  score: number;
  level: 'Low' | 'Medium' | 'High';
  source: 'Manual' | 'AssessmentEngine' | 'Other';
  createdAt: string;
  notes?: string;
}
