/* eslint-disable no-use-before-define */
export interface HouseHold {
  teams: Team[];
  householdScore?: any;
  createdAt: string;
  pets: any[];
  attachments: any[];
  address: Address[];
  org: Org;
  lastVisit: string;
  headOfHouse: HeadOfHouse;
  updatedAt: string;
  title: string;
  kind: string;
  type: string;
  id: string;
  members: Member[];
}

interface Member {
  auth?: any;
  color: string;
  createdAt: string;
  roles: any[];
  attachments: any[];
  id: string;
  sexualIdentity: string;
  gender: string;
  org: any[];
  middleName?: any;
  type: string;
  schools: any[];
  pronouns: string;
  phones: any[];
  ethnicity: string;
  email: string;
  dob: string;
  status: string;
  lastName: string;
  referredBy: string;
  firstName: string;
  updatedAt: string;
  score?: string;
  address: any[];
  lang: string;
  genderIdentity: string;
  lastAt?: string;
}

interface HeadOfHouse {
  id?: any;
}

interface Org {
  id: string;
}

export interface Address {
  team: any[];
  county: string;
  country: string;
  lon?: any;
  street2?: any;
  street: string;
  school: any[];
  note?: any;
  lat?: any;
  zip: string;
  updatedAt: string;
  createdAt: string;
  kind: string;
  state: string;
  member: any[];
  org: any[];
  household: any[];
  id: string;
  city: string;
  network: any[];
}

interface Team {
  name: string;
  updatedAt: string;
  createdAt: string;
  id: string;
  org: any[];
}


export interface HouseholdTag {
  id?: string;
  key: string;
  name: string;
  color: string;
  updatedAt?: string;
  createdAt?: string;
}
