/* eslint-disable no-use-before-define */
import { Carrier } from './service-types';

export interface Network {
    phones: any[];
    id: string;
    updatedAt: string;
    status: string;
    contact?: string;
    types: string[];
    address: Address[];
    createdAt: string;
    services: Service[];
    name: string;
    org: Org;
    email?: string;
    website?: any;
    carriers: any[];
  }

interface Org {
    id: string;
  }

interface Address{
    street: string;
    street2: string;
    city: string;
    state: string;
    zip: string;
    kind: string;
}
  interface Service {
    updatedAt: string;
    status: string;
    desc: string;
    id: string;
    type: string;
    name: string;
    createdAt: string;
    service: string;
    org: Org;
  }

export interface ConnectedNetwork {
  id: string;
  name: string;
  desc?: string;
  addresses: Array<{ address: string }>;
  phones: Array<{ number: string }>;
}

export interface ConnectedNetworksResponse {
  data: {
    org: {
      children: ConnectedNetwork[];
    };
  };
}

export interface ConnectedNetworkService {
  id: string;
  title: string;
  description?: string;
  durationMins: number;
  kind: 'queue' | 'appointment';
  nextAvailableAppt?: Array<{
    startIso: string;
  }>;
}

export interface ConnectedNetworkDetails {
  id: string;
  name: string;
  desc?: string;
  addresses: Array<{ address: string }>;
  phones: Array<{ number: string }>;
  services: ConnectedNetworkService[];
}

export interface ConnectedNetworkDetailsResponse {
  data: {
    org: ConnectedNetworkDetails;
  };
}

export interface NetworkProvider {
  id: string;
  firstName: string;
  lastName: string;
}

export interface NetworkService {
  id: string;
  title: string;
  kind: 'queue' | 'appointment';
  durationMins: number;
}

export interface NetworkOverviewResponse {
  data: {
    org: {
      providers: {
        nodes: NetworkProvider[];
      };
      services: NetworkService[];
    };
  };
}

export interface AvailableTimesRequest {
  org: string;
  service: string;
  date: string;  
}

export interface AvailableTimesResponse {
  data: {
    org: {
      service: {
        id: string;
        title: string;
        availableTimesIso: string[];
      };
    };
  };
}

export interface MemberSearchItem {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  attachments?: Array<{
    id: string;
    url: string;
    kind: string;
  }>;
  org: {
    id: string;
  };
}

export interface MemberSearchResponse {
  items: MemberSearchItem[];
  metadata: {
    page: number;
    total: number;
    per: number;
  };
}

export interface AppointmentBookingRequest {
  person: string;
  provider: string;
  kind: string;
  org: string;
  service: string;
  startEpoch: number;
  endEpoch: number;
  reason?: string;
}

export interface AppointmentBookingResponse {
  data: {
    apptBook: {
      errors: string[];
      success: boolean;
      appointment: {
        id: string;
      };
    };
  };
}

export interface NetworkInviteRequest {
  contact: string;
  email: string;
  from: string;
}

export interface NetworkInviteResponse {
  status: number;
  message?: string;
}
